package com.haitao.backend.controller;

import com.haitao.backend.annotation.ActionLog;
import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.ErrorMessages;
import com.haitao.backend.common.Result;
import com.haitao.backend.dto.*;
import com.haitao.backend.entity.User;
import com.haitao.backend.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 用户管理控制器（仅管理员可访问）
 */
@RestController
@RequestMapping("/admin/users")
@RequireAdmin("用户管理需要管理员权限")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 创建用户
     */
    @PostMapping
    @ActionLog(actionType = "CREATE_USER", description = "创建用户：#{#arg0.username}", targetId = "#result.data.id")
    public Result<User> createUser(@RequestBody CreateUserRequest request, HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        User user = userService.createUser(request, operatorId);
        return ApiResponse.success(ErrorMessages.USER_CREATED, user);
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    @ActionLog(actionType = "UPDATE_USER", description = "更新用户信息：#{#arg1.username}", targetId = "#arg0")
    public Result<User> updateUser(@PathVariable Long userId,
                                  @RequestBody UpdateUserRequest request,
                                  HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        User user = userService.updateUser(userId, request, operatorId);
        return ApiResponse.success(ErrorMessages.USER_UPDATED, user);
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    @ActionLog(actionType = "DELETE_USER", description = "删除用户ID：#{#arg0}", targetId = "#arg0")
    public Result<Void> deleteUser(@PathVariable Long userId, HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        userService.deleteUser(userId, operatorId);
        return ApiResponse.success(ErrorMessages.USER_DELETED);
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @ActionLog(actionType = "BATCH_DELETE_USER", description = "批量删除用户，数量：#{#arg0.size()}")
    public Result<Void> batchDeleteUsers(@RequestBody List<Long> userIds, HttpServletRequest httpRequest) {
        if (userIds == null || userIds.isEmpty()) {
            return ApiResponse.badRequest("请选择要删除的用户");
        }
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        userService.batchDeleteUsers(userIds, operatorId);
        return ApiResponse.success("批量删除成功");
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/{userId}/reset-password")
    public Result<Void> resetPassword(@PathVariable Long userId, @RequestBody ResetPasswordRequest request, HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        userService.resetPassword(userId, request, operatorId);
        return ApiResponse.success(ErrorMessages.PASSWORD_RESET);
    }

    /**
     * 启用/停用用户
     */
    @PostMapping("/{userId}/toggle-status")
    public Result<Void> toggleUserStatus(@PathVariable Long userId,
                                        HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        userService.toggleUserStatus(userId, operatorId);
        return ApiResponse.success(ErrorMessages.STATUS_CHANGED);
    }
    
    /**
     * 分页查询用户列表
     */
    @GetMapping
    public Result<PageResponse<UserListResponse>> getUserList(PageRequest request, HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        PageResponse<UserListResponse> response = userService.getUserList(request, operatorId);
        return ApiResponse.success(response);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{userId}")
    public Result<User> getUserDetail(@PathVariable Long userId) {
        User user = userService.getUserById(userId);
        if (user == null) {
            return ApiResponse.badRequest(ErrorMessages.USER_NOT_FOUND);
        }

        // 不返回密码
        user.setPassword(null);
        return ApiResponse.success(user);
    }
}
