<template>
  <div id="app">
    <SplashScreen
      :visible="showSplash"
      @finished="handleSplashFinished"
    />
    <transition name="fade" mode="out-in">
      <router-view v-if="!showSplash && isAppReady" />
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useUserStore } from './stores/user'
import SplashScreen from './components/SplashScreen.vue'

const userStore = useUserStore()
const showSplash = ref(false) // 默认不显示
const isAppReady = ref(false)

// 检查是否为首次访问（通过sessionStorage）
const isFirstVisit = !sessionStorage.getItem('app_visited')

onMounted(async () => {
  // 检查用户是否已登录
  const hasToken = localStorage.getItem('token')
  const hasUserInfo = localStorage.getItem('userInfo')

  if (!hasToken || !hasUserInfo) {
    // 用户未登录，显示启动画面（只在首次访问或明确未登录时）
    if (isFirstVisit) {
      showSplash.value = true
      sessionStorage.setItem('app_visited', 'true')
    } else {
      // 非首次访问且未登录，直接显示登录页
      showSplash.value = false
      isAppReady.value = true
    }
  } else {
    // 用户已登录，直接进入应用，不显示启动画面
    showSplash.value = false
    isAppReady.value = true
    sessionStorage.setItem('app_visited', 'true')

    // 立即初始化用户信息
    userStore.initUserInfo()
    return
  }

  // 初始化用户信息
  userStore.initUserInfo()
})

// 监听登录状态变化
watch(() => userStore.isLoggedIn, (newValue) => {
  if (newValue) {
    // 用户登录成功，立即隐藏启动画面并显示应用
    showSplash.value = false
    isAppReady.value = true
  } else if (!newValue && userStore.isInitialized) {
    // 用户退出登录，重新显示启动画面（如果需要的话）
    const hasToken = localStorage.getItem('token')
    if (!hasToken) {
      showSplash.value = true
      isAppReady.value = false
    }
  }
})

const handleSplashFinished = () => {
  showSplash.value = false
  isAppReady.value = true
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>



