# 前端部署指南

## 📋 部署前准备

### 1. 环境配置

复制环境配置文件：
```bash
cp .env.local.example .env.local
```

根据实际情况修改 `.env.local` 文件中的配置：
```bash
# 生产环境API地址
VITE_API_BASE_URL=https://your-api-domain.com

# 其他配置...
```

### 2. 修改生产环境配置

编辑 `.env.production` 文件：
```bash
# API配置 - 修改为实际的生产环境地址
VITE_API_BASE_URL=https://your-domain.com/api
```

## 🚀 部署方式

### 方式一：传统部署

1. **构建项目**
```bash
npm install
npm run build
```

2. **上传文件**
将 `dist/` 目录下的所有文件上传到Web服务器根目录

3. **配置Nginx**
```bash
# 复制nginx配置
cp nginx.conf /etc/nginx/sites-available/order-admin
ln -s /etc/nginx/sites-available/order-admin /etc/nginx/sites-enabled/

# 重启nginx
sudo nginx -t
sudo systemctl reload nginx
```

### 方式二：Docker部署

1. **构建Docker镜像**
```bash
docker build -t order-admin-frontend .
```

2. **运行容器**
```bash
docker run -d \
  --name order-admin-frontend \
  -p 80:80 \
  order-admin-frontend
```

### 方式三：自动化部署

使用提供的部署脚本：
```bash
chmod +x deploy.sh
./deploy.sh
```

## 🔧 Nginx配置说明

### 基础配置
- 启用gzip压缩
- 静态资源缓存
- API代理转发
- Vue Router History模式支持

### 安全配置
- 安全头设置
- 隐藏文件访问限制
- XSS防护

### 性能优化
- 静态资源长期缓存
- 压缩传输
- 代理缓冲

## 🌍 环境变量说明

### 开发环境 (.env.development)
- `VITE_API_BASE_URL`: 开发环境API地址
- `VITE_DEBUG`: 启用调试模式
- `VITE_PERFORMANCE_MONITOR`: 性能监控

### 生产环境 (.env.production)
- `VITE_API_BASE_URL`: 生产环境API地址
- `VITE_DROP_CONSOLE`: 移除console日志
- `VITE_ERROR_MONITOR`: 错误监控

## 📊 构建优化

### 代码分割
- Vue相关库单独打包
- UI组件库独立分包
- 工具库分离打包

### 资源优化
- 图片资源压缩
- CSS代码分割
- 静态资源内联

### 性能监控
- 构建分析工具
- 包大小监控
- 加载性能追踪

## 🔍 故障排查

### 常见问题

1. **API请求失败**
   - 检查 `VITE_API_BASE_URL` 配置
   - 确认后端服务是否正常
   - 检查Nginx代理配置

2. **路由404错误**
   - 确认Nginx配置了 `try_files`
   - 检查Vue Router配置

3. **静态资源加载失败**
   - 检查资源路径配置
   - 确认Nginx静态资源配置

### 调试方法

1. **开发环境调试**
```bash
npm run dev
# 查看控制台环境信息
```

2. **生产环境调试**
```bash
npm run build
npm run preview
```

3. **构建分析**
```bash
npm run build:analyze
```

## 📝 部署检查清单

- [ ] 环境变量配置正确
- [ ] API地址可访问
- [ ] 静态资源正常加载
- [ ] 路由跳转正常
- [ ] 用户登录功能正常
- [ ] 数据展示正常
- [ ] 移动端适配正常
- [ ] 性能指标达标

## 🔄 更新部署

1. **拉取最新代码**
```bash
git pull origin main
```

2. **重新构建**
```bash
npm install
npm run build
```

3. **更新服务器文件**
```bash
# 备份当前版本
cp -r /var/www/html /var/www/html.backup

# 更新文件
cp -r dist/* /var/www/html/
```

4. **验证部署**
访问网站确认功能正常
