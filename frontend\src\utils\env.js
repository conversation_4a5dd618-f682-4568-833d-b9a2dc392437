/**
 * 环境配置工具类
 */

// 获取当前环境
export const getEnv = () => {
  return import.meta.env.MODE
}

// 判断是否为开发环境
export const isDevelopment = () => {
  return import.meta.env.MODE === 'development'
}

// 判断是否为生产环境
export const isProduction = () => {
  return import.meta.env.MODE === 'production'
}

// 获取API基础URL
export const getApiBaseUrl = () => {
  if (isDevelopment()) {
    return '/api'
  }
  return import.meta.env.VITE_API_BASE_URL || '/api'
}

// 获取应用配置
export const getAppConfig = () => {
  return {
    title: import.meta.env.VITE_APP_TITLE || '订单任务管理系统',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    description: import.meta.env.VITE_APP_DESCRIPTION || '企业级订单任务管理平台',
    apiBaseUrl: getApiBaseUrl(),
    apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
    debug: import.meta.env.VITE_DEBUG === 'true',
    performanceMonitor: import.meta.env.VITE_PERFORMANCE_MONITOR === 'true',
    errorMonitor: import.meta.env.VITE_ERROR_MONITOR === 'true'
  }
}

// 获取构建信息
export const getBuildInfo = () => {
  return {
    version: __APP_VERSION__ || '1.0.0',
    buildTime: __BUILD_TIME__ || new Date().toISOString(),
    env: getEnv()
  }
}

// 打印环境信息（仅在开发环境）
export const printEnvInfo = () => {
  if (isDevelopment()) {
    const config = getAppConfig()
    const buildInfo = getBuildInfo()
    
    console.group('🚀 应用环境信息')
    console.log('环境:', getEnv())
    console.log('应用标题:', config.title)
    console.log('应用版本:', config.version)
    console.log('API地址:', config.apiBaseUrl)
    console.log('构建时间:', buildInfo.buildTime)
    console.log('调试模式:', config.debug)
    console.groupEnd()
  }
}

// 导出所有环境变量（开发环境调试用）
export const getAllEnvVars = () => {
  if (isDevelopment()) {
    return import.meta.env
  }
  return {}
}
