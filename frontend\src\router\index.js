import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import Login from '../views/Login.vue'
import Layout from '../layout/index.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: {
          title: '首页',
          icon: 'House',
          requiresAuth: true
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('../views/admin/UserManagement.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'tasks',
        name: 'TaskManagement',
        component: () => import('../views/admin/TaskManagement.vue'),
        meta: {
          title: '任务管理',
          icon: 'Document',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'logs',
        name: 'LogManagement',
        component: () => import('../views/admin/LogManagement.vue'),
        meta: {
          title: '日志管理',
          icon: 'List',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'statistics',
        name: 'IncomeStatistics',
        component: () => import('../views/admin/IncomeStatistics.vue'),
        meta: {
          title: '收入统计',
          icon: 'TrendCharts',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'my-statistics',
        name: 'MyIncomeStatistics',
        component: () => import('../views/admin/MyIncomeStatistics.vue'),
        meta: {
          title: '我的收入',
          icon: 'Wallet',
          requiresAuth: true,
          requiresAdmin: false
        }
      },
      {
        path: 'my-orders',
        name: 'MyOrders',
        component: () => import('../views/user/MyOrders.vue'),
        meta: {
          title: '我的订单',
          icon: 'Document',
          requiresAuth: true,
          requiresAdmin: false,
          userOnly: true  // 只有普通用户可见
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 确保用户状态已初始化
  if (!userStore.isInitialized) {
    userStore.initUserInfo()
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && userStore.isLoggedIn) {
    if (userStore.userInfo.role !== 0) {
      // 非管理员访问管理员页面，重定向到首页
      next('/dashboard')
      return
    }
  }

  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router


