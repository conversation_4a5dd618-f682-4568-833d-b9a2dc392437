package com.haitao.backend.enums;

/**
 * 用户状态枚举
 */
public enum UserStatus {
    
    DISABLED(0, "停用"),
    ENABLED(1, "正常");
    
    private final Integer code;
    private final String name;
    
    UserStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public static UserStatus fromCode(Integer code) {
        for (UserStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}
