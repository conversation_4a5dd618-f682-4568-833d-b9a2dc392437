import request from '../utils/request'

/**
 * 统计分析相关API
 */
export const statisticsApi = {
  
  /**
   * 获取收入统计数据
   * @param {Object} query 查询条件
   * @returns {Promise} 统计数据
   */
  getIncomeStatistics(query) {
    return request({
      url: '/admin/statistics/income',
      method: 'post',
      data: query
    })
  },

  /**
   * 获取用户收入详情
   * @param {number} userId 用户ID
   * @param {Object} query 查询条件
   * @returns {Promise} 用户收入详情
   */
  getUserIncomeDetail(userId, query) {
    return request({
      url: `/admin/statistics/user/${userId}/income`,
      method: 'post',
      data: query
    })
  },

  /**
   * 获取收入趋势图表数据
   * @param {Object} query 查询条件
   * @returns {Promise} 图表数据
   */
  getIncomeChartData(query) {
    return request({
      url: '/admin/statistics/income/chart',
      method: 'post',
      data: query
    })
  },

  /**
   * 获取用户排行榜
   * @param {Object} query 查询条件
   * @returns {Promise} 用户排行数据
   */
  getUserRanking(query) {
    return request({
      url: '/admin/statistics/ranking/users',
      method: 'post',
      data: query
    })
  },

  /**
   * 获取派单员抽成排行榜
   * @param {Object} query 查询条件
   * @returns {Promise} 派单员排行数据
   */
  getDispatcherRanking(query) {
    return request({
      url: '/admin/statistics/ranking/dispatchers',
      method: 'post',
      data: query
    })
  },

  /**
   * 获取当前用户的收入统计
   * @param {Object} query 查询条件
   * @returns {Promise} 个人收入统计
   */
  getMyIncomeStatistics(query) {
    return request({
      url: '/admin/statistics/my-income',
      method: 'post',
      data: query
    })
  },

  /**
   * 获取当前用户的收入详情
   * @param {Object} query 查询条件
   * @returns {Promise} 个人收入详情
   */
  getMyIncomeDetail(query) {
    return request({
      url: '/admin/statistics/my-income/detail',
      method: 'post',
      data: query
    })
  },

  /**
   * 获取统计概览
   * @returns {Promise} 统计概览数据
   */
  getStatisticsOverview() {
    return request({
      url: '/admin/statistics/overview',
      method: 'get'
    })
  }
}

/**
 * 统计查询条件构建器
 */
export const StatisticsQueryBuilder = {
  
  /**
   * 创建基础查询条件
   * @returns {Object} 查询条件对象
   */
  create() {
    return {
      timeDimension: 'month', // year, month, week, day
      startTime: null,
      endTime: null,
      userId: null,
      status: null,
      onlySettled: false,
      statisticsType: 'income', // income, trend, ranking
      sortBy: 'totalAmount', // totalAmount, netIncome, taskCount
      sortOrder: 'desc', // asc, desc
      limit: 10
    }
  },

  /**
   * 设置时间维度
   * @param {Object} query 查询条件
   * @param {string} dimension 时间维度 (year/month/week/day)
   * @returns {Object} 查询条件
   */
  setTimeDimension(query, dimension) {
    query.timeDimension = dimension
    return query
  },

  /**
   * 设置时间范围
   * @param {Object} query 查询条件
   * @param {string} startTime 开始时间
   * @param {string} endTime 结束时间
   * @returns {Object} 查询条件
   */
  setTimeRange(query, startTime, endTime) {
    query.startTime = startTime
    query.endTime = endTime
    return query
  },

  /**
   * 设置最近N天的时间范围
   * @param {Object} query 查询条件
   * @param {number} days 天数
   * @returns {Object} 查询条件
   */
  setRecentDays(query, days) {
    const endTime = new Date()
    const startTime = new Date()
    startTime.setDate(startTime.getDate() - days)
    
    query.endTime = this.formatDateTime(endTime)
    query.startTime = this.formatDateTime(startTime)
    return query
  },

  /**
   * 设置最近N个月的时间范围
   * @param {Object} query 查询条件
   * @param {number} months 月数
   * @returns {Object} 查询条件
   */
  setRecentMonths(query, months) {
    const endTime = new Date()
    const startTime = new Date()
    startTime.setMonth(startTime.getMonth() - months)
    
    query.endTime = this.formatDateTime(endTime)
    query.startTime = this.formatDateTime(startTime)
    return query
  },

  /**
   * 设置当前年度时间范围
   * @param {Object} query 查询条件
   * @returns {Object} 查询条件
   */
  setCurrentYear(query) {
    const now = new Date()
    const startTime = new Date(now.getFullYear(), 0, 1)
    const endTime = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
    
    query.startTime = this.formatDateTime(startTime)
    query.endTime = this.formatDateTime(endTime)
    return query
  },

  /**
   * 设置当前月度时间范围
   * @param {Object} query 查询条件
   * @returns {Object} 查询条件
   */
  setCurrentMonth(query) {
    const now = new Date()
    const startTime = new Date(now.getFullYear(), now.getMonth(), 1)
    const endTime = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
    
    query.startTime = this.formatDateTime(startTime)
    query.endTime = this.formatDateTime(endTime)
    return query
  },

  /**
   * 设置用户筛选
   * @param {Object} query 查询条件
   * @param {number} userId 用户ID
   * @returns {Object} 查询条件
   */
  setUser(query, userId) {
    query.userId = userId
    return query
  },

  /**
   * 设置状态筛选
   * @param {Object} query 查询条件
   * @param {number} status 状态
   * @returns {Object} 查询条件
   */
  setStatus(query, status) {
    query.status = status
    return query
  },

  /**
   * 设置只统计已结算任务
   * @param {Object} query 查询条件
   * @param {boolean} onlySettled 是否只统计已结算
   * @returns {Object} 查询条件
   */
  setOnlySettled(query, onlySettled) {
    query.onlySettled = onlySettled
    return query
  },

  /**
   * 设置排序条件
   * @param {Object} query 查询条件
   * @param {string} sortBy 排序字段
   * @param {string} sortOrder 排序方向
   * @returns {Object} 查询条件
   */
  setSort(query, sortBy, sortOrder = 'desc') {
    query.sortBy = sortBy
    query.sortOrder = sortOrder
    return query
  },

  /**
   * 设置返回记录数限制
   * @param {Object} query 查询条件
   * @param {number} limit 限制数量
   * @returns {Object} 查询条件
   */
  setLimit(query, limit) {
    query.limit = limit
    return query
  },

  /**
   * 设置是否包含管理员
   * @param {Object} query 查询条件
   * @param {boolean} includeAdmin 是否包含管理员
   * @returns {Object} 查询条件
   */
  setIncludeAdmin(query, includeAdmin) {
    query.includeAdmin = includeAdmin
    return query
  },

  /**
   * 格式化日期时间
   * @param {Date} date 日期对象
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
}

export default statisticsApi
