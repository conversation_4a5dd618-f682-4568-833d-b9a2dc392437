/**
 * 图表数据处理工具
 * 提供数据转换、格式化、优化等功能
 */

/**
 * 安全的数据转换
 */
export function safeDataTransform(data, defaultValue = 0) {
  if (data === null || data === undefined || isNaN(data)) {
    return defaultValue
  }
  return Number(data)
}

/**
 * 处理图表数据
 */
export function processChartData(rawData) {
  if (!rawData || !rawData.trendData) {
    return {
      xAxisLabels: [],
      totalAmountSeries: [],
      netIncomeSeries: [],
      dispatcherFeeSeries: [],
      taskCountSeries: []
    }
  }

  const { trendData } = rawData
  
  return {
    xAxisLabels: trendData.map(item => item.timeLabel || ''),
    totalAmountSeries: trendData.map(item => safeDataTransform(item.totalAmount)),
    netIncomeSeries: trendData.map(item => safeDataTransform(item.netIncome)),
    dispatcherFeeSeries: trendData.map(item => safeDataTransform(item.dispatcherFee)),
    taskCountSeries: trendData.map(item => Math.round(safeDataTransform(item.taskCount)))
  }
}

/**
 * 数据采样 - 当数据点过多时进行采样
 */
export function sampleData(data, maxPoints = 50) {
  if (!Array.isArray(data) || data.length <= maxPoints) {
    return data
  }

  const step = Math.ceil(data.length / maxPoints)
  const sampledData = []
  
  for (let i = 0; i < data.length; i += step) {
    sampledData.push(data[i])
  }
  
  return sampledData
}

/**
 * 处理大数据量的图表数据
 */
export function processLargeChartData(rawData, maxPoints = 50) {
  if (!rawData || !rawData.trendData) {
    return processChartData(rawData)
  }

  const { trendData } = rawData
  const sampledTrendData = sampleData(trendData, maxPoints)
  
  return {
    xAxisLabels: sampledTrendData.map(item => item.timeLabel || ''),
    totalAmountSeries: sampledTrendData.map(item => safeDataTransform(item.totalAmount)),
    netIncomeSeries: sampledTrendData.map(item => safeDataTransform(item.netIncome)),
    dispatcherFeeSeries: sampledTrendData.map(item => safeDataTransform(item.dispatcherFee)),
    taskCountSeries: sampledTrendData.map(item => Math.round(safeDataTransform(item.taskCount)))
  }
}

/**
 * 数据聚合 - 按时间维度聚合数据
 */
export function aggregateDataByTime(data, timeDimension) {
  if (!Array.isArray(data)) return []

  const aggregated = new Map()
  
  data.forEach(item => {
    let key = item.timeLabel
    
    // 根据时间维度调整聚合键
    if (timeDimension === 'month' && key) {
      key = key.substring(0, 7) // YYYY-MM
    } else if (timeDimension === 'year' && key) {
      key = key.substring(0, 4) // YYYY
    }
    
    if (!aggregated.has(key)) {
      aggregated.set(key, {
        timeLabel: key,
        totalAmount: 0,
        netIncome: 0,
        dispatcherFee: 0,
        taskCount: 0,
        completedCount: 0
      })
    }
    
    const existing = aggregated.get(key)
    existing.totalAmount += safeDataTransform(item.totalAmount)
    existing.netIncome += safeDataTransform(item.netIncome)
    existing.dispatcherFee += safeDataTransform(item.dispatcherFee)
    existing.taskCount += safeDataTransform(item.taskCount)
    existing.completedCount += safeDataTransform(item.completedCount)
  })
  
  return Array.from(aggregated.values()).sort((a, b) => a.timeLabel.localeCompare(b.timeLabel))
}

/**
 * 计算数据统计信息
 */
export function calculateDataStats(data) {
  if (!Array.isArray(data) || data.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      sum: 0,
      count: 0
    }
  }

  const values = data.map(item => safeDataTransform(item)).filter(val => val > 0)
  
  if (values.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      sum: 0,
      count: 0
    }
  }

  const sum = values.reduce((acc, val) => acc + val, 0)
  const min = Math.min(...values)
  const max = Math.max(...values)
  const avg = sum / values.length

  return {
    min,
    max,
    avg: Math.round(avg * 100) / 100,
    sum,
    count: values.length
  }
}

/**
 * 格式化数值显示
 */
export function formatValue(value, type = 'number') {
  const num = safeDataTransform(value)
  
  switch (type) {
    case 'currency':
      return `¥${num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'percentage':
      return `${num.toFixed(2)}%`
    case 'compact':
      if (num >= 10000) {
        return `${(num / 10000).toFixed(1)}万`
      }
      return num.toLocaleString('zh-CN')
    default:
      return num.toLocaleString('zh-CN')
  }
}

/**
 * 生成颜色渐变
 */
export function generateGradientColors(baseColor, count) {
  const colors = []
  const hsl = hexToHsl(baseColor)
  
  for (let i = 0; i < count; i++) {
    const lightness = Math.max(0.3, Math.min(0.8, hsl.l + (i - count / 2) * 0.1))
    colors.push(hslToHex(hsl.h, hsl.s, lightness))
  }
  
  return colors
}

/**
 * 颜色转换工具
 */
function hexToHsl(hex) {
  const r = parseInt(hex.slice(1, 3), 16) / 255
  const g = parseInt(hex.slice(3, 5), 16) / 255
  const b = parseInt(hex.slice(5, 7), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h, s, l = (max + min) / 2

  if (max === min) {
    h = s = 0
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return { h, s, l }
}

function hslToHex(h, s, l) {
  const hue2rgb = (p, q, t) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1/6) return p + (q - p) * 6 * t
    if (t < 1/2) return q
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
    return p
  }

  let r, g, b

  if (s === 0) {
    r = g = b = l
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }

  const toHex = (c) => {
    const hex = Math.round(c * 255).toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/**
 * 数据验证
 */
export function validateChartData(data) {
  const errors = []
  
  if (!data) {
    errors.push('数据不能为空')
    return { isValid: false, errors }
  }
  
  if (!data.xAxisLabels || !Array.isArray(data.xAxisLabels)) {
    errors.push('X轴标签数据格式错误')
  }
  
  const seriesKeys = ['totalAmountSeries', 'netIncomeSeries', 'dispatcherFeeSeries', 'taskCountSeries']
  seriesKeys.forEach(key => {
    if (data[key] && !Array.isArray(data[key])) {
      errors.push(`${key} 数据格式错误`)
    }
  })
  
  // 检查数据长度一致性
  if (data.xAxisLabels && data.totalAmountSeries) {
    if (data.xAxisLabels.length !== data.totalAmountSeries.length) {
      errors.push('X轴标签与数据系列长度不一致')
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export default {
  safeDataTransform,
  processChartData,
  processLargeChartData,
  sampleData,
  aggregateDataByTime,
  calculateDataStats,
  formatValue,
  generateGradientColors,
  validateChartData
}
