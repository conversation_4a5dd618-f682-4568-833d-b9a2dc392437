package com.haitao.backend.exception;

import com.haitao.backend.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理异常并返回简洁的响应格式
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleValidationException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        logger.warn("参数校验失败: {} | 请求: {} {}", errorMessage, request.getMethod(), request.getRequestURI());
        return Result.badRequest("参数校验失败: " + errorMessage);
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBindException(BindException ex, HttpServletRequest request) {
        String errorMessage = ex.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        logger.warn("数据绑定失败: {} | 请求: {} {}", errorMessage, request.getMethod(), request.getRequestURI());
        return Result.badRequest("数据绑定失败: " + errorMessage);
    }
    

    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBusinessException(BusinessException ex, HttpServletRequest request) {
        logger.warn("业务异常: {} | 请求: {} {}", ex.getMessage(), request.getMethod(), request.getRequestURI());
        return Result.badRequest(ex.getMessage());
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleIllegalArgumentException(IllegalArgumentException ex, HttpServletRequest request) {
        logger.warn("非法参数: {} | 请求: {} {}", ex.getMessage(), request.getMethod(), request.getRequestURI());
        return Result.badRequest("参数错误: " + ex.getMessage());
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleRuntimeException(RuntimeException ex, HttpServletRequest request) {
        logger.error("运行时异常: {} | 请求: {} {}", ex.getMessage(), request.getMethod(), request.getRequestURI(), ex);
        return Result.error("系统内部错误，请稍后重试");
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleException(Exception ex, HttpServletRequest request) {
        logger.error("未知异常: {} | 请求: {} {}", ex.getMessage(), request.getMethod(), request.getRequestURI(), ex);
        return Result.error("系统异常，请联系管理员");
    }
}
