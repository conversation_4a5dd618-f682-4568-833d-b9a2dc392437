package com.haitao.backend.service;

import com.haitao.backend.dto.*;
import com.haitao.backend.entity.User;

import java.util.List;

public interface UserService {

    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);

    /**
     * 创建用户（仅管理员）
     */
    User createUser(CreateUserRequest request, Long operatorId);

    /**
     * 更新用户信息（仅管理员）
     * 注意：用户名（username）创建后不可修改
     */
    User updateUser(Long userId, UpdateUserRequest request, Long operatorId);

    /**
     * 删除用户（仅管理员）
     */
    void deleteUser(Long userId, Long operatorId);

    /**
     * 批量删除用户（仅管理员）
     */
    void batchDeleteUsers(List<Long> userIds, Long operatorId);

    /**
     * 重置用户密码（仅管理员）
     */
    void resetPassword(Long userId, ResetPasswordRequest request, Long operatorId);

    /**
     * 启用/停用用户（仅管理员）
     */
    void toggleUserStatus(Long userId, Long operatorId);

    /**
     * 分页查询用户列表（仅管理员）
     */
    PageResponse<UserListResponse> getUserList(PageRequest request, Long operatorId);

    /**
     * 根据ID获取用户信息
     */
    User getUserById(Long id);

    /**
     * 检查用户是否为管理员
     */
    boolean isAdmin(Long userId);
}