<template>
  <div class="log-management">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="登录结果">
          <el-select v-model="searchForm.loginResult" placeholder="选择登录结果" clearable style="width: 120px">
            <el-option label="成功" :value="1" />
            <el-option label="失败" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名或姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input
            v-model="searchForm.ipAddress"
            placeholder="搜索IP地址"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getLogList" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 日志列表 -->
    <div class="table-section">
      <el-table
        :data="logList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'loginTime', order: 'descending' }"
        :header-cell-style="{ textAlign: 'center', fontWeight: 'bold' }"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="loginResult" label="登录结果" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.loginResult === 1 ? 'success' : 'danger'">
              {{ row.loginResult === 1 ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ipAddress" label="IP地址" width="140" />
        <el-table-column prop="userAgent" label="浏览器信息" min-width="200" show-overflow-tooltip />
        <el-table-column prop="loginTime" label="登录时间" width="180" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { logApi } from '../../api/log'

// 响应式数据
const loading = ref(false)
const logList = ref([])
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  loginResult: null,
  keyword: '',
  ipAddress: '',
  startTime: '',
  endTime: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 监听时间范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchForm.startTime = newVal[0]
    searchForm.endTime = newVal[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
})

// 获取日志列表
const getLogList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    }
    
    const response = await logApi.getLoginLogList(params)
    if (response.code === 200) {
      logList.value = response.data.records
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取日志列表失败:', error)
    ElMessage.error('获取日志列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    loginResult: null,
    keyword: '',
    ipAddress: '',
    startTime: '',
    endTime: ''
  })
  dateRange.value = []
  pagination.page = 1
  getLogList()
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getLogList()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  getLogList()
}

// 页面加载时获取数据
onMounted(() => {
  getLogList()
})
</script>

<style scoped>
.log-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table th) {
  background-color: #f8f9fa !important;
}

:deep(.el-pagination) {
  justify-content: flex-end;
}
</style>
