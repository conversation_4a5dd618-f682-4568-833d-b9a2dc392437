package com.haitao.backend.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 任务查询请求DTO
 */
@Data
public class TaskQueryRequest {
    
    /** 关键词搜索（项目名称、订单编号） */
    private String keyword;
    
    /** 任务状态 */
    private Integer status;
    
    /** 被分配成员ID */
    private Long assignedUserId;
    
    /** 任务创建人ID */
    private Long createdBy;
    
    /** 接单开始时间 */
    private LocalDateTime orderTimeStart;
    
    /** 接单结束时间 */
    private LocalDateTime orderTimeEnd;
    
    /** 截止开始时间 */
    private LocalDateTime deadlineStart;
    
    /** 截止结束时间 */
    private LocalDateTime deadlineEnd;
    
    /** 页码 */
    private Integer page = 1;
    
    /** 每页大小 */
    private Integer size = 10;
    
    /** 排序字段 */
    private String sortField = "create_time";
    
    /** 排序方向 */
    private String sortOrder = "desc";
}
