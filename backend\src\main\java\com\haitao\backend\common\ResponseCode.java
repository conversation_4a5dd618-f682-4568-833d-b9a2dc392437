package com.haitao.backend.common;

/**
 * 响应状态码常量
 */
public class ResponseCode {
    
    // 成功状态码
    public static final int SUCCESS = 200;
    
    // 客户端错误状态码
    public static final int BAD_REQUEST = 400;
    public static final int UNAUTHORIZED = 401;
    public static final int FORBIDDEN = 403;
    public static final int NOT_FOUND = 404;
    public static final int METHOD_NOT_ALLOWED = 405;
    public static final int CONFLICT = 409;
    public static final int UNPROCESSABLE_ENTITY = 422;
    public static final int TOO_MANY_REQUESTS = 429;
    
    // 服务器错误状态码
    public static final int INTERNAL_SERVER_ERROR = 500;
    public static final int NOT_IMPLEMENTED = 501;
    public static final int BAD_GATEWAY = 502;
    public static final int SERVICE_UNAVAILABLE = 503;
    public static final int GATEWAY_TIMEOUT = 504;
    
    // 业务状态码（自定义）
    public static final int BUSINESS_ERROR = 1000;
    public static final int VALIDATION_ERROR = 1001;
    public static final int DUPLICATE_ERROR = 1002;
    public static final int RESOURCE_NOT_FOUND = 1003;
    public static final int PERMISSION_DENIED = 1004;
    public static final int OPERATION_FAILED = 1005;
    
    // 状态码描述
    public static String getDescription(int code) {
        switch (code) {
            case SUCCESS:
                return "操作成功";
            case BAD_REQUEST:
                return "请求参数错误";
            case UNAUTHORIZED:
                return "未授权访问";
            case FORBIDDEN:
                return "禁止访问";
            case NOT_FOUND:
                return "资源不存在";
            case INTERNAL_SERVER_ERROR:
                return "服务器内部错误";
            case BUSINESS_ERROR:
                return "业务处理失败";
            case VALIDATION_ERROR:
                return "数据验证失败";
            case DUPLICATE_ERROR:
                return "数据重复";
            case RESOURCE_NOT_FOUND:
                return "资源未找到";
            case PERMISSION_DENIED:
                return "权限不足";
            case OPERATION_FAILED:
                return "操作失败";
            default:
                return "未知状态";
        }
    }
}
