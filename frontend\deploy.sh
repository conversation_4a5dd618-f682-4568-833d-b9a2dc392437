#!/bin/bash

# 前端部署脚本

set -e

echo "🚀 开始部署前端应用..."

# 检查Node.js版本
echo "📋 检查环境..."
node --version
npm --version

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 构建生产版本
echo "🔨 构建生产版本..."
npm run build

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 构建失败，dist目录不存在"
    exit 1
fi

echo "✅ 构建完成"

# 可选：上传到服务器
# echo "📤 上传到服务器..."
# rsync -avz --delete dist/ user@server:/path/to/web/root/

# 可选：Docker部署
# echo "🐳 Docker部署..."
# docker build -t order-admin-frontend .
# docker run -d -p 80:80 --name order-admin-frontend order-admin-frontend

echo "🎉 部署完成！"
