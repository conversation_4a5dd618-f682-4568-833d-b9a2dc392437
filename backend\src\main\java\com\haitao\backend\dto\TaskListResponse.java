package com.haitao.backend.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 任务列表响应DTO
 */
@Data
public class TaskListResponse {
    
    private Long id;
    
    /** 项目名称 */
    private String projectName;
    
    /** 订单编号 */
    private String orderNumber;
    
    /** 项目总价 */
    private BigDecimal totalPrice;
    
    /** 抽成比例 */
    private BigDecimal commissionRate;
    
    /** 实得收入 */
    private BigDecimal netIncome;
    
    /** 抽成金额 */
    private BigDecimal commissionAmount;
    
    /** 派单员抽成金额 */
    private BigDecimal dispatcherFee;
    
    /** 被分配成员ID */
    private Long assignedUserId;
    
    /** 被分配成员姓名 */
    private String assignedUserName;
    
    /** 任务创建人ID */
    private Long createdBy;
    
    /** 任务创建人姓名 */
    private String createdByName;
    
    /** 接单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    
    /** 客户要求完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;
    
    /** 任务状态 */
    private Integer status;
    
    /** 任务状态名称 */
    private String statusName;
    
    /** 备注信息 */
    private String remarks;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
