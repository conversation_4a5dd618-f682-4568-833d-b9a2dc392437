<template>
  <div class="dashboard">
    <!-- 粒子背景 -->
    <div id="particles-js" class="particles-container"></div>

    <!-- 背景图片 -->
    <div class="background-image"></div>

    <!-- 中央欢迎内容 -->
    <div class="welcome-container">
      <div class="welcome-content">
        <h1 class="welcome-title" ref="titleRef"></h1>
        <p class="welcome-subtitle" ref="subtitleRef"></p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, nextTick } from 'vue'
import { useUserStore } from '../stores/user'
import { gsap } from 'gsap'

const userStore = useUserStore()

// 模板引用
const titleRef = ref(null)
const subtitleRef = ref(null)

// 打字机效果文本数组
const textSets = [
  {
    title: '欢迎使用海泡订单系统',
    subtitle: '专业的订单管理解决方案'
  },
  {
    title: '高效管理您的订单',
    subtitle: '让业务流程更加顺畅'
  },
  {
    title: '智能分配任务',
    subtitle: '提升团队协作效率'
  }
]

let currentTextIndex = 0
let typewriterTimeline = null

// 创建气泡粒子效果
const createParticles = () => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  const particlesContainer = document.getElementById('particles-js')

  if (!particlesContainer) return

  canvas.width = window.innerWidth
  canvas.height = window.innerHeight
  canvas.style.position = 'absolute'
  canvas.style.top = '0'
  canvas.style.left = '0'
  canvas.style.pointerEvents = 'none'
  canvas.style.zIndex = '1'

  particlesContainer.appendChild(canvas)

  const bubbles = []
  const bubbleCount = 30

  // 气泡颜色数组
  const bubbleColors = [
    'rgba(135, 206, 235, 0.6)',  // 天蓝色
    'rgba(173, 216, 230, 0.6)',  // 浅蓝色
    'rgba(176, 224, 230, 0.6)',  // 粉蓝色
    'rgba(175, 238, 238, 0.6)',  // 淡青色
    'rgba(240, 248, 255, 0.6)',  // 爱丽丝蓝
    'rgba(230, 230, 250, 0.6)',  // 薰衣草色
  ]

  // 创建气泡
  for (let i = 0; i < bubbleCount; i++) {
    bubbles.push({
      x: Math.random() * canvas.width,
      y: canvas.height + Math.random() * 200, // 从底部开始
      size: Math.random() * 15 + 5, // 5-20px
      speed: Math.random() * 2 + 0.5, // 上升速度
      opacity: Math.random() * 0.7 + 0.3,
      color: bubbleColors[Math.floor(Math.random() * bubbleColors.length)],
      wobble: Math.random() * 0.02 + 0.01, // 摆动幅度
      wobbleOffset: Math.random() * Math.PI * 2, // 摆动偏移
      life: 0, // 生命周期
      maxLife: Math.random() * 300 + 200, // 最大生命周期
      popAnimation: 0, // 爆破动画进度
      isPopping: false // 是否正在爆破
    })
  }

  // 绘制气泡函数
  const drawBubble = (bubble) => {
    if (bubble.isPopping) {
      // 爆破效果
      const progress = bubble.popAnimation / 20
      const radius = bubble.size * (1 + progress * 2)
      const alpha = 1 - progress

      ctx.save()
      ctx.globalAlpha = alpha * bubble.opacity
      ctx.beginPath()
      ctx.arc(bubble.x, bubble.y, radius, 0, Math.PI * 2)
      ctx.strokeStyle = bubble.color.replace('0.6', alpha * 0.8)
      ctx.lineWidth = 2
      ctx.stroke()

      // 绘制爆破粒子
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI * 2 / 6) * i
        const particleX = bubble.x + Math.cos(angle) * radius * 0.7
        const particleY = bubble.y + Math.sin(angle) * radius * 0.7

        ctx.beginPath()
        ctx.arc(particleX, particleY, 2, 0, Math.PI * 2)
        ctx.fillStyle = bubble.color.replace('0.6', alpha * 0.6)
        ctx.fill()
      }
      ctx.restore()
    } else {
      // 正常气泡
      ctx.save()
      ctx.globalAlpha = bubble.opacity

      // 主气泡
      ctx.beginPath()
      ctx.arc(bubble.x, bubble.y, bubble.size, 0, Math.PI * 2)

      // 渐变填充
      const gradient = ctx.createRadialGradient(
        bubble.x - bubble.size * 0.3, bubble.y - bubble.size * 0.3, 0,
        bubble.x, bubble.y, bubble.size
      )
      gradient.addColorStop(0, bubble.color.replace('0.6', '0.8'))
      gradient.addColorStop(0.7, bubble.color.replace('0.6', '0.4'))
      gradient.addColorStop(1, bubble.color.replace('0.6', '0.1'))

      ctx.fillStyle = gradient
      ctx.fill()

      // 气泡边框
      ctx.strokeStyle = bubble.color.replace('0.6', '0.8')
      ctx.lineWidth = 1
      ctx.stroke()

      // 高光效果
      ctx.beginPath()
      ctx.arc(
        bubble.x - bubble.size * 0.3,
        bubble.y - bubble.size * 0.3,
        bubble.size * 0.3,
        0, Math.PI * 2
      )
      ctx.fillStyle = 'rgba(255, 255, 255, 0.6)'
      ctx.fill()

      // 小高光
      ctx.beginPath()
      ctx.arc(
        bubble.x - bubble.size * 0.15,
        bubble.y - bubble.size * 0.15,
        bubble.size * 0.1,
        0, Math.PI * 2
      )
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)'
      ctx.fill()

      ctx.restore()
    }
  }

  // 动画循环
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    bubbles.forEach((bubble) => {
      if (bubble.isPopping) {
        bubble.popAnimation++
        if (bubble.popAnimation > 20) {
          // 重置气泡
          bubble.x = Math.random() * canvas.width
          bubble.y = canvas.height + Math.random() * 200
          bubble.size = Math.random() * 15 + 5
          bubble.speed = Math.random() * 2 + 0.5
          bubble.opacity = Math.random() * 0.7 + 0.3
          bubble.color = bubbleColors[Math.floor(Math.random() * bubbleColors.length)]
          bubble.life = 0
          bubble.maxLife = Math.random() * 300 + 200
          bubble.popAnimation = 0
          bubble.isPopping = false
        }
      } else {
        // 更新气泡位置
        bubble.y -= bubble.speed
        bubble.x += Math.sin(bubble.life * bubble.wobble + bubble.wobbleOffset) * 0.5
        bubble.life++

        // 检查是否到达顶部或生命周期结束
        if (bubble.y < -bubble.size || bubble.life > bubble.maxLife) {
          bubble.isPopping = true
        }

        // 随机爆破
        if (Math.random() < 0.001) {
          bubble.isPopping = true
        }
      }

      drawBubble(bubble)
    })

    requestAnimationFrame(animate)
  }

  animate()

  // 鼠标交互 - 点击产生新气泡
  canvas.addEventListener('click', (e) => {
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // 添加新气泡
    for (let i = 0; i < 3; i++) {
      bubbles.push({
        x: x + (Math.random() - 0.5) * 50,
        y: y + (Math.random() - 0.5) * 50,
        size: Math.random() * 10 + 8,
        speed: Math.random() * 3 + 1,
        opacity: Math.random() * 0.8 + 0.4,
        color: bubbleColors[Math.floor(Math.random() * bubbleColors.length)],
        wobble: Math.random() * 0.03 + 0.01,
        wobbleOffset: Math.random() * Math.PI * 2,
        life: 0,
        maxLife: Math.random() * 200 + 150,
        popAnimation: 0,
        isPopping: false
      })
    }

    // 移除多余的气泡
    if (bubbles.length > bubbleCount + 20) {
      bubbles.splice(0, bubbles.length - bubbleCount)
    }
  })

  // 窗口大小改变时调整canvas
  window.addEventListener('resize', () => {
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
  })
}

// 循环打字机动画函数
const typewriterAnimation = async () => {
  await nextTick()

  if (!titleRef.value || !subtitleRef.value) return

  const currentTexts = textSets[currentTextIndex]

  // 清空初始内容
  titleRef.value.textContent = ''
  subtitleRef.value.textContent = ''

  // 创建时间线
  typewriterTimeline = gsap.timeline()

  // 延迟开始
  typewriterTimeline.to({}, { duration: 0.5 })

  // 标题打字效果
  for (let i = 0; i <= currentTexts.title.length; i++) {
    typewriterTimeline.call(() => {
      titleRef.value.textContent = currentTexts.title.slice(0, i)
    }).to({}, { duration: 0.08 })
  }

  // 暂停
  typewriterTimeline.to({}, { duration: 0.5 })

  // 副标题打字效果
  for (let i = 0; i <= currentTexts.subtitle.length; i++) {
    typewriterTimeline.call(() => {
      subtitleRef.value.textContent = currentTexts.subtitle.slice(0, i)
    }).to({}, { duration: 0.06 })
  }

  // 显示完整文本后暂停
  typewriterTimeline.to({}, { duration: 2 })

  // 删除副标题
  for (let i = currentTexts.subtitle.length; i >= 0; i--) {
    typewriterTimeline.call(() => {
      subtitleRef.value.textContent = currentTexts.subtitle.slice(0, i)
    }).to({}, { duration: 0.04 })
  }

  // 删除标题
  for (let i = currentTexts.title.length; i >= 0; i--) {
    typewriterTimeline.call(() => {
      titleRef.value.textContent = currentTexts.title.slice(0, i)
    }).to({}, { duration: 0.04 })
  }

  // 暂停后开始下一轮
  typewriterTimeline.to({}, { duration: 1 })
    .call(() => {
      currentTextIndex = (currentTextIndex + 1) % textSets.length
      typewriterAnimation() // 递归调用开始下一轮
    })
}

onMounted(async () => {
  // 创建自定义粒子效果
  setTimeout(() => {
    createParticles()
  }, 100)

  // 启动打字机动画
  setTimeout(() => {
    typewriterAnimation()
  }, 500)
})

onUnmounted(() => {
  // 清理打字机动画
  if (typewriterTimeline) {
    typewriterTimeline.kill()
  }

  // 清理粒子容器
  const particlesContainer = document.getElementById('particles-js')
  if (particlesContainer) {
    particlesContainer.innerHTML = ''
  }
})


</script>

<style scoped>
.dashboard {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg,
    #0c1e3d 0%,
    #1e3a8a 25%,
    #1e40af 50%,
    #0369a1 75%,
    #0284c7 100%);
  z-index: 1;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/image/dashboard.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 0;
}

.welcome-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
  text-align: center;
}

.welcome-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.welcome-title {
  color: white;
  font-size: 3rem;
  font-weight: 400;
  margin: 0;
  letter-spacing: 3px;
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.8),
    0 0 20px rgba(59, 130, 246, 0.6),
    0 0 30px rgba(59, 130, 246, 0.4);
  min-height: 1.2em;
  display: inline-block;
  position: relative;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Arial, sans-serif;
}

.welcome-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  margin: 0 0 30px 0;
  font-weight: 400;
  letter-spacing: 2px;
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.6),
    0 0 20px rgba(59, 130, 246, 0.4);
  min-height: 1.2em;
  display: inline-block;
  position: relative;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Arial, sans-serif;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .welcome-subtitle {
    font-size: 1.2rem;
    letter-spacing: 1px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .welcome-subtitle {
    font-size: 1rem;
    letter-spacing: 0.5px;
  }

  .welcome-content {
    gap: 15px;
  }
}
</style>