package com.haitao.backend.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户收入详情响应DTO
 */
@Data
public class UserIncomeDetailResponse {
    
    /** 用户基本信息 */
    private UserInfo userInfo;
    
    /** 收入统计汇总 */
    private IncomeSummary incomeSummary;
    
    /** 任务详情列表 */
    private List<TaskIncomeDetail> taskDetails;
    
    /**
     * 用户基本信息
     */
    @Data
    public static class UserInfo {
        /** 用户ID */
        private Long userId;
        
        /** 用户名 */
        private String username;
        
        /** 真实姓名 */
        private String realName;
        
        /** 角色名称 */
        private String roleName;
        
        /** 联系方式 */
        private String phone;
        
        /** 邮箱 */
        private String email;
    }
    
    /**
     * 收入统计汇总
     */
    @Data
    public static class IncomeSummary {
        /** 总接单金额 */
        private BigDecimal totalAmount;
        
        /** 总实得收入 */
        private BigDecimal totalNetIncome;
        
        /** 总任务数量 */
        private Long totalTaskCount;
        
        /** 已完成任务数量 */
        private Long completedTaskCount;
        
        /** 已结算任务数量 */
        private Long settledTaskCount;
        
        /** 进行中任务数量 */
        private Long inProgressTaskCount;
        
        /** 平均任务金额 */
        private BigDecimal averageTaskAmount;
        
        /** 完成率 */
        private BigDecimal completionRate;
        
        /** 结算率 */
        private BigDecimal settlementRate;
    }
    
    /**
     * 任务收入详情
     */
    @Data
    public static class TaskIncomeDetail {
        /** 任务ID */
        private Long taskId;
        
        /** 项目名称 */
        private String projectName;
        
        /** 订单编号 */
        private String orderNumber;
        
        /** 项目总价 */
        private BigDecimal totalPrice;
        
        /** 抽成比例 */
        private BigDecimal commissionRate;
        
        /** 实得收入 */
        private BigDecimal netIncome;
        
        /** 抽成金额 */
        private BigDecimal commissionAmount;
        
        /** 任务状态 */
        private Integer status;
        
        /** 任务状态名称 */
        private String statusName;
        
        /** 接单时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime orderTime;
        
        /** 完成时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime completedTime;
        
        /** 创建人姓名 */
        private String createdByName;
        
        /** 备注 */
        private String remarks;
    }
}
