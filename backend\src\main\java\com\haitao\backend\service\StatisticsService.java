package com.haitao.backend.service;

import com.haitao.backend.dto.StatisticsQueryRequest;
import com.haitao.backend.dto.IncomeStatisticsResponse;
import com.haitao.backend.dto.UserIncomeDetailResponse;

/**
 * 统计服务接口
 */
public interface StatisticsService {
    
    /**
     * 获取收入统计数据
     * @param query 查询条件
     * @param operatorId 操作者ID（用于权限控制）
     * @return 收入统计响应
     */
    IncomeStatisticsResponse getIncomeStatistics(StatisticsQueryRequest query, Long operatorId);
    
    /**
     * 获取用户收入详情
     * @param userId 用户ID
     * @param query 查询条件
     * @param operatorId 操作者ID（用于权限控制）
     * @return 用户收入详情响应
     */
    UserIncomeDetailResponse getUserIncomeDetail(Long userId, StatisticsQueryRequest query, Long operatorId);
    
    /**
     * 获取收入趋势数据（用于图表展示）
     * @param query 查询条件
     * @param operatorId 操作者ID（用于权限控制）
     * @return 图表数据
     */
    IncomeStatisticsResponse.ChartData getIncomeChartData(StatisticsQueryRequest query, Long operatorId);
    
    /**
     * 获取用户排行榜数据
     * @param query 查询条件
     * @param operatorId 操作者ID（用于权限控制）
     * @return 用户排行数据
     */
    IncomeStatisticsResponse getUserRanking(StatisticsQueryRequest query, Long operatorId);
    
    /**
     * 获取派单员抽成排行榜
     * @param query 查询条件
     * @param operatorId 操作者ID（用于权限控制）
     * @return 派单员排行数据
     */
    IncomeStatisticsResponse getDispatcherRanking(StatisticsQueryRequest query, Long operatorId);
}
