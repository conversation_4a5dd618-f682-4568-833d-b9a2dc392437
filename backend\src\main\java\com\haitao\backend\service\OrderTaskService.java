package com.haitao.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.dto.*;

import java.util.List;

/**
 * 订单任务服务接口
 */
public interface OrderTaskService {
    
    /**
     * 创建任务
     */
    OrderTask createTask(CreateTaskRequest request, Long operatorId);
    
    /**
     * 更新任务
     */
    OrderTask updateTask(Long taskId, UpdateTaskRequest request, Long operatorId);
    
    /**
     * 删除任务
     */
    void deleteTask(Long taskId, Long operatorId);

    /**
     * 批量删除任务
     */
    void batchDeleteTasks(List<Long> taskIds, Long operatorId);
    
    /**
     * 分页查询任务列表
     */
    IPage<TaskListResponse> getTaskList(TaskQueryRequest query);
    
    /**
     * 获取任务详情
     */
    TaskListResponse getTaskDetail(Long taskId);
    
    /**
     * 更新任务状态
     */
    void updateTaskStatus(Long taskId, Integer status, Long operatorId);
    
    /**
     * 分配任务给成员
     */
    void assignTask(Long taskId, Long assignedUserId, Long operatorId);
}
