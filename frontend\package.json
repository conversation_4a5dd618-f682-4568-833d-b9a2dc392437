{"name": "frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:local": "vite --mode development --host", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist", "preview": "vite preview", "preview:dist": "vite preview --port 4173", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .vue,.js,.ts --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"axios": "^1.6.2", "echarts": "^5.6.0", "element-plus": "^2.10.4", "gsap": "^3.13.0", "particles.js": "^2.0.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-echarts": "^6.7.3", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4", "autoprefixer": "^10.4.16", "cssnano": "^6.0.1", "postcss": "^8.4.32", "rimraf": "^5.0.5", "terser": "^5.24.0", "vite-bundle-analyzer": "^0.7.0"}}