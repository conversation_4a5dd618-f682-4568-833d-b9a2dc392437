{"name": "frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "echarts": "^5.6.0", "element-plus": "^2.10.4", "gsap": "^3.13.0", "particles.js": "^2.0.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-echarts": "^6.7.3", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4"}}