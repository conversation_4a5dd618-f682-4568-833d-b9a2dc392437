/**
 * 统计数据管理器 - 性能优化版本
 * 提供数据缓存、防抖、批量处理等功能
 */

import { ref, reactive } from 'vue'
import { statisticsApi } from '../api/statistics'

class StatisticsManager {
  constructor() {
    // 数据缓存
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
    
    // 请求队列
    this.requestQueue = new Map()
    
    // 防抖定时器
    this.debounceTimers = new Map()
    
    // 加载状态管理
    this.loadingStates = reactive({})
    
    // 错误状态管理
    this.errorStates = reactive({})
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(apiMethod, params) {
    return `${apiMethod}_${JSON.stringify(params)}`
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cacheItem) {
    return cacheItem && (Date.now() - cacheItem.timestamp) < this.cacheTimeout
  }

  /**
   * 设置缓存
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    const cacheItem = this.cache.get(key)
    if (this.isCacheValid(cacheItem)) {
      return cacheItem.data
    }
    this.cache.delete(key)
    return null
  }

  /**
   * 清除缓存
   */
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }

  /**
   * 防抖请求
   */
  debounceRequest(key, fn, delay = 300) {
    return new Promise((resolve, reject) => {
      // 清除之前的定时器
      if (this.debounceTimers.has(key)) {
        clearTimeout(this.debounceTimers.get(key))
      }

      // 设置新的定时器
      const timer = setTimeout(async () => {
        try {
          const result = await fn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.debounceTimers.delete(key)
        }
      }, delay)

      this.debounceTimers.set(key, timer)
    })
  }

  /**
   * 批量请求处理
   */
  async batchRequest(requests) {
    const results = await Promise.allSettled(requests)
    return results.map((result, index) => ({
      index,
      status: result.status,
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null
    }))
  }

  /**
   * 获取收入统计数据（带缓存和防抖）
   */
  async getIncomeStatistics(query, options = {}) {
    const { useCache = true, debounce = true, loadingKey = 'income' } = options
    
    try {
      // 设置加载状态
      this.loadingStates[loadingKey] = true
      this.errorStates[loadingKey] = null

      const cacheKey = this.generateCacheKey('getIncomeStatistics', query)
      
      // 检查缓存
      if (useCache) {
        const cachedData = this.getCache(cacheKey)
        if (cachedData) {
          this.loadingStates[loadingKey] = false
          return cachedData
        }
      }

      // 防抖处理
      const requestFn = () => statisticsApi.getIncomeStatistics(query)
      const data = debounce 
        ? await this.debounceRequest(cacheKey, requestFn)
        : await requestFn()

      // 缓存结果
      if (useCache) {
        this.setCache(cacheKey, data)
      }

      return data
    } catch (error) {
      this.errorStates[loadingKey] = error
      throw error
    } finally {
      this.loadingStates[loadingKey] = false
    }
  }

  /**
   * 获取用户排行数据
   */
  async getUserRanking(query, options = {}) {
    const { useCache = true, loadingKey = 'ranking' } = options
    
    try {
      this.loadingStates[loadingKey] = true
      this.errorStates[loadingKey] = null

      const cacheKey = this.generateCacheKey('getUserRanking', query)
      
      if (useCache) {
        const cachedData = this.getCache(cacheKey)
        if (cachedData) {
          this.loadingStates[loadingKey] = false
          return cachedData
        }
      }

      const data = await statisticsApi.getUserRanking(query)
      
      if (useCache) {
        this.setCache(cacheKey, data)
      }

      return data
    } catch (error) {
      this.errorStates[loadingKey] = error
      throw error
    } finally {
      this.loadingStates[loadingKey] = false
    }
  }

  /**
   * 获取个人收入统计
   */
  async getMyIncomeStatistics(query, options = {}) {
    const { useCache = true, loadingKey = 'myIncome' } = options
    
    try {
      this.loadingStates[loadingKey] = true
      this.errorStates[loadingKey] = null

      const cacheKey = this.generateCacheKey('getMyIncomeStatistics', query)
      
      if (useCache) {
        const cachedData = this.getCache(cacheKey)
        if (cachedData) {
          this.loadingStates[loadingKey] = false
          return cachedData
        }
      }

      const data = await statisticsApi.getMyIncomeStatistics(query)
      
      if (useCache) {
        this.setCache(cacheKey, data)
      }

      return data
    } catch (error) {
      this.errorStates[loadingKey] = error
      throw error
    } finally {
      this.loadingStates[loadingKey] = false
    }
  }

  /**
   * 预加载数据
   */
  async preloadData(queries) {
    const requests = queries.map(({ method, query }) => {
      switch (method) {
        case 'getIncomeStatistics':
          return this.getIncomeStatistics(query, { useCache: true, debounce: false })
        case 'getUserRanking':
          return this.getUserRanking(query, { useCache: true })
        case 'getMyIncomeStatistics':
          return this.getMyIncomeStatistics(query, { useCache: true })
        default:
          return Promise.resolve(null)
      }
    })

    return this.batchRequest(requests)
  }

  /**
   * 获取加载状态
   */
  getLoadingState(key) {
    return this.loadingStates[key] || false
  }

  /**
   * 获取错误状态
   */
  getErrorState(key) {
    return this.errorStates[key] || null
  }

  /**
   * 清除状态
   */
  clearStates() {
    Object.keys(this.loadingStates).forEach(key => {
      this.loadingStates[key] = false
    })
    Object.keys(this.errorStates).forEach(key => {
      this.errorStates[key] = null
    })
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 清除所有定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()
    
    // 清除缓存
    this.cache.clear()
    
    // 清除状态
    this.clearStates()
  }
}

// 创建全局实例
export const statisticsManager = new StatisticsManager()

// 导出类供其他地方使用
export default StatisticsManager
