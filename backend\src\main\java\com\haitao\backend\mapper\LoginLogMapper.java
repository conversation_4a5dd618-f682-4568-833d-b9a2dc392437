package com.haitao.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.entity.LoginLog;
import com.haitao.backend.dto.LoginLogResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 登录日志Mapper
 */
@Mapper
public interface LoginLogMapper extends BaseMapper<LoginLog> {
    
    /**
     * 分页查询登录日志（带用户信息）
     */
    @Select("SELECT ll.*, u.username, u.real_name " +
            "FROM login_log ll " +
            "LEFT JOIN user u ON ll.user_id = u.id " +
            "${ew.customSqlSegment}")
    IPage<LoginLogResponse> selectLoginLogWithUserInfo(Page<LoginLogResponse> page, @Param("ew") Object wrapper);
}
