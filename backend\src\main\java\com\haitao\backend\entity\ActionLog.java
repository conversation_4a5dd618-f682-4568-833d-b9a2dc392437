package com.haitao.backend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 操作日志实体类
 */
@TableName("action_log")
public class ActionLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 执行操作的用户ID
     */
    private Long userId;
    
    /**
     * 操作类型
     */
    private String actionType;
    
    /**
     * 被操作对象的ID
     */
    private Long targetId;
    
    /**
     * 操作说明
     */
    private String description;
    
    /**
     * 操作来源IP
     */
    private String ipAddress;
    
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actionTime;
    
    // 构造函数
    public ActionLog() {}
    
    public ActionLog(Long userId, String actionType, Long targetId, String description, String ipAddress) {
        this.userId = userId;
        this.actionType = actionType;
        this.targetId = targetId;
        this.description = description;
        this.ipAddress = ipAddress;
        this.actionTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getActionType() {
        return actionType;
    }
    
    public void setActionType(String actionType) {
        this.actionType = actionType;
    }
    
    public Long getTargetId() {
        return targetId;
    }
    
    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public LocalDateTime getActionTime() {
        return actionTime;
    }
    
    public void setActionTime(LocalDateTime actionTime) {
        this.actionTime = actionTime;
    }
}
