<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haitao.backend.mapper.OrderTaskMapper">

    <!-- 任务列表查询结果映射 -->
    <resultMap id="TaskListResponseMap" type="com.haitao.backend.dto.TaskListResponse">
        <id column="id" property="id"/>
        <result column="project_name" property="projectName"/>
        <result column="order_number" property="orderNumber"/>
        <result column="total_price" property="totalPrice"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="net_income" property="netIncome"/>
        <result column="commission_amount" property="commissionAmount"/>
        <result column="dispatcher_fee" property="dispatcherFee"/>
        <result column="assigned_user_id" property="assignedUserId"/>
        <result column="assigned_user_name" property="assignedUserName"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_by_name" property="createdByName"/>
        <result column="order_time" property="orderTime"/>
        <result column="deadline" property="deadline"/>
        <result column="status" property="status"/>
        <result column="status_name" property="statusName"/>
        <result column="remarks" property="remarks"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询任务列表 -->
    <select id="selectTaskListWithUserInfo" resultMap="TaskListResponseMap">
        SELECT 
            ot.id,
            ot.project_name,
            ot.order_number,
            ot.total_price,
            ot.commission_rate,
            ot.net_income,
            ot.commission_amount,
            ot.dispatcher_fee,
            ot.assigned_user_id,
            au.real_name as assigned_user_name,
            ot.created_by,
            cu.real_name as created_by_name,
            ot.order_time,
            ot.deadline,
            ot.status,
            CASE ot.status
                WHEN 0 THEN '待处理'
                WHEN 1 THEN '进行中'
                WHEN 2 THEN '已完成'
                WHEN 3 THEN '客户已取消'
                WHEN 4 THEN '待结算'
                WHEN 5 THEN '已结算'
                ELSE '未知状态'
            END as status_name,
            ot.remarks,
            ot.create_time,
            ot.update_time
        FROM order_task ot
        LEFT JOIN user au ON ot.assigned_user_id = au.id
        LEFT JOIN user cu ON ot.created_by = cu.id
        WHERE ot.is_deleted = 0
        <if test="query.keyword != null and query.keyword != ''">
            AND (ot.project_name LIKE CONCAT('%', #{query.keyword}, '%') 
                 OR ot.order_number LIKE CONCAT('%', #{query.keyword}, '%'))
        </if>
        <if test="query.status != null">
            AND ot.status = #{query.status}
        </if>
        <if test="query.assignedUserId != null">
            AND ot.assigned_user_id = #{query.assignedUserId}
        </if>
        <if test="query.createdBy != null">
            AND ot.created_by = #{query.createdBy}
        </if>
        <if test="query.orderTimeStart != null">
            AND ot.order_time >= #{query.orderTimeStart}
        </if>
        <if test="query.orderTimeEnd != null">
            AND ot.order_time &lt;= #{query.orderTimeEnd}
        </if>
        <if test="query.deadlineStart != null">
            AND ot.deadline >= #{query.deadlineStart}
        </if>
        <if test="query.deadlineEnd != null">
            AND ot.deadline &lt;= #{query.deadlineEnd}
        </if>
        ORDER BY 
        <choose>
            <when test="query.sortField == 'total_price'">ot.total_price</when>
            <when test="query.sortField == 'order_time'">ot.order_time</when>
            <when test="query.sortField == 'deadline'">ot.deadline</when>
            <when test="query.sortField == 'status'">ot.status</when>
            <otherwise>ot.create_time</otherwise>
        </choose>
        <choose>
            <when test="query.sortOrder == 'asc'">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
    </select>

    <!-- 查询任务详情 -->
    <select id="selectTaskDetailById" resultMap="TaskListResponseMap">
        SELECT 
            ot.id,
            ot.project_name,
            ot.order_number,
            ot.total_price,
            ot.commission_rate,
            ot.net_income,
            ot.commission_amount,
            ot.dispatcher_fee,
            ot.assigned_user_id,
            au.real_name as assigned_user_name,
            ot.created_by,
            cu.real_name as created_by_name,
            ot.order_time,
            ot.deadline,
            ot.status,
            CASE ot.status
                WHEN 0 THEN '待处理'
                WHEN 1 THEN '进行中'
                WHEN 2 THEN '已完成'
                WHEN 3 THEN '客户已取消'
                WHEN 4 THEN '待结算'
                WHEN 5 THEN '已结算'
                ELSE '未知状态'
            END as status_name,
            ot.remarks,
            ot.create_time,
            ot.update_time
        FROM order_task ot
        LEFT JOIN user au ON ot.assigned_user_id = au.id
        LEFT JOIN user cu ON ot.created_by = cu.id
        WHERE ot.id = #{id} AND ot.is_deleted = 0
    </select>

    <!-- 生成订单编号 -->
    <select id="generateOrderNumber" resultType="string">
        SELECT CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d'),
                     LPAD(IFNULL(MAX(SUBSTRING(order_number, 12)), 0) + 1, 4, '0'))
        FROM order_task
        WHERE order_number LIKE CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d'), '%')
    </select>

    <!-- ==================== 统计查询SQL ==================== -->

    <!-- 总体统计数据 -->
    <select id="selectOverallStatistics" resultType="com.haitao.backend.dto.IncomeStatisticsResponse$OverallStatistics">
        SELECT
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as totalNetIncome,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.dispatcher_fee ELSE 0 END), 0) as totalDispatcherFee,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.commission_amount ELSE 0 END), 0) as totalCommission,
            COUNT(*) as totalTaskCount,
            SUM(CASE WHEN ot.status = 2 THEN 1 ELSE 0 END) as completedTaskCount,
            SUM(CASE WHEN ot.status = 5 THEN 1 ELSE 0 END) as settledTaskCount,
            COALESCE(AVG(ot.total_price), 0) as averageTaskAmount,
            COALESCE(AVG(ot.commission_rate), 0) as averageCommissionRate
        FROM order_task ot
        WHERE ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.userId != null">
            AND ot.assigned_user_id = #{query.userId}
        </if>
        <if test="query.status != null">
            AND ot.status = #{query.status}
        </if>
        <if test="query.onlySettled != null and query.onlySettled">
            AND ot.status = 5
        </if>
    </select>

    <!-- 时间趋势数据 -->
    <select id="selectTrendData" resultType="com.haitao.backend.dto.IncomeStatisticsResponse$TrendData">
        SELECT
            <choose>
                <when test="query.timeDimension == 'year'">
                    DATE_FORMAT(ot.order_time, '%Y') as timeLabel,
                </when>
                <when test="query.timeDimension == 'month'">
                    DATE_FORMAT(ot.order_time, '%Y-%m') as timeLabel,
                </when>
                <when test="query.timeDimension == 'week'">
                    CONCAT(YEAR(ot.order_time), '-W', LPAD(WEEK(ot.order_time, 1), 2, '0')) as timeLabel,
                </when>
                <otherwise>
                    DATE_FORMAT(ot.order_time, '%Y-%m-%d') as timeLabel,
                </otherwise>
            </choose>
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as netIncome,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.dispatcher_fee ELSE 0 END), 0) as dispatcherFee,
            COUNT(*) as taskCount,
            SUM(CASE WHEN ot.status IN (2, 5) THEN 1 ELSE 0 END) as completedCount
        FROM order_task ot
        WHERE ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.userId != null">
            AND ot.assigned_user_id = #{query.userId}
        </if>
        <if test="query.status != null">
            AND ot.status = #{query.status}
        </if>
        <if test="query.onlySettled != null and query.onlySettled">
            AND ot.status = 5
        </if>
        GROUP BY timeLabel
        ORDER BY timeLabel
    </select>

    <!-- 用户排行数据 -->
    <select id="selectUserRankingData" resultType="com.haitao.backend.dto.IncomeStatisticsResponse$UserRankingData">
        SELECT
            u.id as userId,
            u.real_name as realName,
            u.username,
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as netIncome,
            COUNT(*) as taskCount,
            SUM(CASE WHEN ot.status IN (2, 5) THEN 1 ELSE 0 END) as completedCount,
            COALESCE(AVG(ot.total_price), 0) as averageAmount
        FROM user u
        LEFT JOIN order_task ot ON u.id = ot.assigned_user_id AND ot.is_deleted = 0
        <where>
            <choose>
                <when test="query.includeAdmin != null and query.includeAdmin == true">
                    u.role IN (0, 1)
                </when>
                <otherwise>
                    u.role = 1
                </otherwise>
            </choose>
            <if test="query.startTime != null">
                AND ot.order_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND ot.order_time &lt;= #{query.endTime}
            </if>
            <if test="query.status != null">
                AND ot.status = #{query.status}
            </if>
            <if test="query.onlySettled != null and query.onlySettled">
                AND ot.status = 5
            </if>
        </where>
        GROUP BY u.id, u.real_name, u.username
        HAVING taskCount > 0
        <choose>
            <when test="query.sortBy == 'netIncome'">
                ORDER BY netIncome DESC
            </when>
            <when test="query.sortBy == 'taskCount'">
                ORDER BY taskCount DESC
            </when>
            <otherwise>
                ORDER BY totalAmount DESC
            </otherwise>
        </choose>
        <if test="query.limit != null">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 用户收入汇总 -->
    <select id="selectUserIncomeSummary" resultType="com.haitao.backend.dto.UserIncomeDetailResponse$IncomeSummary">
        SELECT
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as totalNetIncome,
            COUNT(*) as totalTaskCount,
            SUM(CASE WHEN ot.status = 2 THEN 1 ELSE 0 END) as completedTaskCount,
            SUM(CASE WHEN ot.status = 5 THEN 1 ELSE 0 END) as settledTaskCount,
            SUM(CASE WHEN ot.status = 1 THEN 1 ELSE 0 END) as inProgressTaskCount,
            COALESCE(AVG(ot.total_price), 0) as averageTaskAmount,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND(SUM(CASE WHEN ot.status IN (2, 5) THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0
            END as completionRate,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND(SUM(CASE WHEN ot.status = 5 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0
            END as settlementRate
        FROM order_task ot
        WHERE ot.assigned_user_id = #{userId} AND ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.status != null">
            AND ot.status = #{query.status}
        </if>
    </select>

    <!-- 用户任务收入详情列表 -->
    <select id="selectUserTaskIncomeDetails" resultType="com.haitao.backend.dto.UserIncomeDetailResponse$TaskIncomeDetail">
        SELECT
            ot.id as taskId,
            ot.project_name as projectName,
            ot.order_number as orderNumber,
            ot.total_price as totalPrice,
            ot.commission_rate as commissionRate,
            ot.net_income as netIncome,
            ot.commission_amount as commissionAmount,
            ot.status,
            CASE ot.status
                WHEN 0 THEN '待处理'
                WHEN 1 THEN '进行中'
                WHEN 2 THEN '已完成'
                WHEN 3 THEN '客户已取消'
                WHEN 4 THEN '待结算'
                WHEN 5 THEN '已结算'
                ELSE '未知状态'
            END as statusName,
            ot.order_time as orderTime,
            ot.update_time as completedTime,
            cu.real_name as createdByName,
            ot.remarks
        FROM order_task ot
        LEFT JOIN user cu ON ot.created_by = cu.id
        WHERE ot.assigned_user_id = #{userId} AND ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.status != null">
            AND ot.status = #{query.status}
        </if>
        ORDER BY ot.order_time DESC
    </select>

    <!-- 按月统计收入趋势 -->
    <select id="selectMonthlyIncomeTrend" resultType="map">
        SELECT
            DATE_FORMAT(ot.order_time, '%Y-%m') as month,
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as netIncome,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.dispatcher_fee ELSE 0 END), 0) as dispatcherFee,
            COUNT(*) as taskCount
        FROM order_task ot
        WHERE ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.userId != null">
            AND ot.assigned_user_id = #{query.userId}
        </if>
        GROUP BY DATE_FORMAT(ot.order_time, '%Y-%m')
        ORDER BY month
    </select>

    <!-- 按周统计收入趋势 -->
    <select id="selectWeeklyIncomeTrend" resultType="map">
        SELECT
            CONCAT(YEAR(ot.order_time), '-W', LPAD(WEEK(ot.order_time, 1), 2, '0')) as week,
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as netIncome,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.dispatcher_fee ELSE 0 END), 0) as dispatcherFee,
            COUNT(*) as taskCount
        FROM order_task ot
        WHERE ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.userId != null">
            AND ot.assigned_user_id = #{query.userId}
        </if>
        GROUP BY YEAR(ot.order_time), WEEK(ot.order_time, 1)
        ORDER BY week
    </select>

    <!-- 按日统计收入趋势 -->
    <select id="selectDailyIncomeTrend" resultType="map">
        SELECT
            DATE_FORMAT(ot.order_time, '%Y-%m-%d') as day,
            COALESCE(SUM(ot.total_price), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.net_income ELSE 0 END), 0) as netIncome,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.dispatcher_fee ELSE 0 END), 0) as dispatcherFee,
            COUNT(*) as taskCount
        FROM order_task ot
        WHERE ot.is_deleted = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        <if test="query.userId != null">
            AND ot.assigned_user_id = #{query.userId}
        </if>
        GROUP BY DATE_FORMAT(ot.order_time, '%Y-%m-%d')
        ORDER BY day
    </select>

    <!-- 派单员抽成排行 -->
    <select id="selectDispatcherFeeRanking" resultType="map">
        SELECT
            cu.id as userId,
            cu.real_name as realName,
            cu.username,
            COALESCE(SUM(CASE WHEN ot.status IN (2, 5) THEN ot.dispatcher_fee ELSE 0 END), 0) as totalDispatcherFee,
            COUNT(*) as taskCount
        FROM user cu
        LEFT JOIN order_task ot ON cu.id = ot.created_by AND ot.is_deleted = 0
        WHERE cu.role = 0
        <if test="query.startTime != null">
            AND ot.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND ot.order_time &lt;= #{query.endTime}
        </if>
        GROUP BY cu.id, cu.real_name, cu.username
        HAVING taskCount > 0
        ORDER BY totalDispatcherFee DESC
        <if test="query.limit != null">
            LIMIT #{query.limit}
        </if>
    </select>

</mapper>
