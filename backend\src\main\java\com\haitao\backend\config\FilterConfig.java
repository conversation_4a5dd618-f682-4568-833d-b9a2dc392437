package com.haitao.backend.config;

import com.haitao.backend.filter.RequestResponseLoggingFilter;
import com.haitao.backend.filter.XssFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 过滤器配置类
 */
@Configuration
public class FilterConfig {
    
    /**
     * 注册XSS过滤器
     */
    @Bean
    public FilterRegistrationBean<XssFilter> xssFilterRegistration(XssFilter xssFilter) {
        FilterRegistrationBean<XssFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(xssFilter);
        registration.addUrlPatterns("/*");
        registration.setName("xssFilter");
        registration.setOrder(1);
        return registration;
    }
    
    /**
     * 注册请求响应日志过滤器
     */
    @Bean
    public FilterRegistrationBean<RequestResponseLoggingFilter> loggingFilterRegistration(RequestResponseLoggingFilter loggingFilter) {
        FilterRegistrationBean<RequestResponseLoggingFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(loggingFilter);
        registration.addUrlPatterns("/*");
        registration.setName("requestResponseLoggingFilter");
        registration.setOrder(2);
        return registration;
    }
}
