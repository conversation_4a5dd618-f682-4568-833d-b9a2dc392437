package com.haitao.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.Result;
import com.haitao.backend.dto.LoginLogResponse;
import com.haitao.backend.dto.LogQueryRequest;
import com.haitao.backend.service.LoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录日志管理控制器
 */
@RestController
@RequestMapping("/admin/login-logs")
@RequireAdmin("登录日志查看需要管理员权限")
public class LoginLogController {
    
    @Autowired
    private LoginLogService loginLogService;
    
    /**
     * 分页查询登录日志
     */
    @GetMapping
    public Result<IPage<LoginLogResponse>> getLoginLogList(LogQueryRequest request) {
        IPage<LoginLogResponse> logList = loginLogService.getLoginLogList(request);
        return ApiResponse.success(logList);
    }
}
