package com.haitao.backend.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 更新用户请求DTO
 * 注意：用户名（username）创建后不可修改
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateUserRequest {

    private String realName;

    private Integer role; // 0=管理员，1=普通成员

    private String phone;

    private String email;

    private Integer status; // 1=正常，0=停用
}
