<template>
  <div class="income-chart">
    <v-chart 
      :option="chartOption" 
      :style="{ height: height, width: width }"
      :autoresize="true"
      @click="handleChartClick"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON>hart, BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  Bar<PERSON>hart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
])

const props = defineProps({
  // 图表类型：line, bar, pie
  type: {
    type: String,
    default: 'line'
  },
  // 图表数据
  data: {
    type: Object,
    required: true
  },
  // 图表标题
  title: {
    type: String,
    default: '收入统计'
  },
  // 图表高度
  height: {
    type: String,
    default: '400px'
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // 是否显示数据缩放
  showDataZoom: {
    type: Boolean,
    default: true
  },
  // 是否显示工具栏
  showToolbox: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['chart-click'])

// 图表配置
const chartOption = computed(() => {
  // 检查数据是否有效
  if (!props.data || !props.data.xAxisLabels || props.data.xAxisLabels.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    }
  }

  const baseOption = {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params) {
        if (!params || params.length === 0) return ''
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          let value = param.value || 0
          // 如果是任务数量，显示为整数
          if (param.seriesName === '任务数量') {
            value = Math.round(value)
          } else if (typeof value === 'number') {
            value = value.toLocaleString()
          }
          result += `${param.marker}${param.seriesName}: ${value}${param.seriesName === '任务数量' ? ' 个' : ''}<br/>`
        })
        return result
      }
    },
    legend: {
      top: props.title ? 50 : 20,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: props.showDataZoom ? '15%' : '3%',
      top: props.title ? 80 : 60,
      containLabel: true
    }
  }

  // 根据图表类型生成不同配置
  if (props.type === 'line') {
    const hasTaskCount = props.data.taskCountSeries && props.data.taskCountSeries.length > 0

    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: props.data.xAxisLabels || [],
        boundaryGap: false,
        axisLabel: {
          rotate: props.data.xAxisLabels.length > 10 ? 45 : 0
        }
      },
      yAxis: hasTaskCount ? [
        {
          type: 'value',
          name: '金额 (元)',
          position: 'left',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '数量 (个)',
          position: 'right',
          axisLabel: {
            formatter: function(value) {
              return Math.round(value)
            }
          },
          minInterval: 1,
          splitNumber: 5
        }
      ] : {
        type: 'value',
        name: '金额 (元)',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: generateLineSeries(),
      dataZoom: props.showDataZoom ? [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 0,
          end: 100,
          height: 20
        }
      ] : undefined,
      toolbox: props.showToolbox ? {
        feature: {
          saveAsImage: { title: '保存为图片' },
          dataZoom: { title: { zoom: '区域缩放', back: '缩放还原' } },
          restore: { title: '还原' }
        }
      } : undefined
    }
  } else if (props.type === 'bar') {
    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: props.data.xAxisLabels || [],
        axisLabel: {
          rotate: props.data.xAxisLabels.length > 8 ? 45 : 0
        }
      },
      yAxis: {
        type: 'value',
        name: '金额 (元)',
        axisLabel: {
          formatter: function(value) {
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + '万'
            }
            return value.toLocaleString()
          }
        }
      },
      series: generateBarSeries(),
      dataZoom: props.showDataZoom && props.data.xAxisLabels.length > 10 ? [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 0,
          end: 100,
          height: 20
        }
      ] : undefined,
      toolbox: props.showToolbox ? {
        feature: {
          saveAsImage: { title: '保存为图片' },
          restore: { title: '还原' }
        }
      } : undefined
    }
  } else if (props.type === 'pie') {
    return {
      ...baseOption,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: generatePieSeries(),
      toolbox: props.showToolbox ? {
        feature: {
          saveAsImage: { title: '保存为图片' }
        }
      } : undefined
    }
  }

  return baseOption
})

// 生成折线图系列
function generateLineSeries() {
  const series = []

  if (props.data.totalAmountSeries && props.data.totalAmountSeries.length > 0) {
    series.push({
      name: '接单总金额',
      type: 'line',
      data: props.data.totalAmountSeries.map(val => val || 0),
      smooth: true,
      lineStyle: { width: 3 },
      itemStyle: { color: '#409EFF' },
      symbol: 'circle',
      symbolSize: 6,
      yAxisIndex: 0
    })
  }

  if (props.data.netIncomeSeries && props.data.netIncomeSeries.length > 0) {
    series.push({
      name: '实得收入',
      type: 'line',
      data: props.data.netIncomeSeries.map(val => val || 0),
      smooth: true,
      lineStyle: { width: 3 },
      itemStyle: { color: '#67C23A' },
      symbol: 'circle',
      symbolSize: 6,
      yAxisIndex: 0
    })
  }

  if (props.data.dispatcherFeeSeries && props.data.dispatcherFeeSeries.length > 0) {
    series.push({
      name: '派单员抽成',
      type: 'line',
      data: props.data.dispatcherFeeSeries.map(val => val || 0),
      smooth: true,
      lineStyle: { width: 3 },
      itemStyle: { color: '#E6A23C' },
      symbol: 'circle',
      symbolSize: 6,
      yAxisIndex: 0
    })
  }

  if (props.data.taskCountSeries && props.data.taskCountSeries.length > 0) {
    series.push({
      name: '任务数量',
      type: 'line',
      data: props.data.taskCountSeries.map(val => val || 0),
      smooth: true,
      lineStyle: { width: 3 },
      itemStyle: { color: '#F56C6C' },
      symbol: 'diamond',
      symbolSize: 8,
      yAxisIndex: 1
    })
  }

  return series
}

// 生成柱状图系列
function generateBarSeries() {
  const series = []

  if (props.data.totalAmountSeries && props.data.totalAmountSeries.length > 0) {
    series.push({
      name: '接单总金额',
      type: 'bar',
      data: props.data.totalAmountSeries.map(val => val || 0),
      itemStyle: {
        color: '#409EFF',
        borderRadius: [4, 4, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: '#66b1ff'
        }
      }
    })
  }

  if (props.data.netIncomeSeries && props.data.netIncomeSeries.length > 0) {
    series.push({
      name: '实得收入',
      type: 'bar',
      data: props.data.netIncomeSeries.map(val => val || 0),
      itemStyle: {
        color: '#67C23A',
        borderRadius: [4, 4, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: '#85ce61'
        }
      }
    })
  }

  if (props.data.dispatcherFeeSeries && props.data.dispatcherFeeSeries.length > 0) {
    series.push({
      name: '派单员抽成',
      type: 'bar',
      data: props.data.dispatcherFeeSeries.map(val => val || 0),
      itemStyle: {
        color: '#E6A23C',
        borderRadius: [4, 4, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: '#ebb563'
        }
      }
    })
  }

  return series
}

// 生成饼图系列
function generatePieSeries() {
  return [{
    name: '收入分布',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '60%'],
    data: props.data.pieData || [],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    label: {
      formatter: '{b}: {c} ({d}%)'
    }
  }]
}

// 图表点击事件
function handleChartClick(params) {
  emit('chart-click', params)
}
</script>

<style scoped>
.income-chart {
  width: 100%;
  height: 100%;
}
</style>
