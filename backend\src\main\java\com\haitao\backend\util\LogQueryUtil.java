package com.haitao.backend.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haitao.backend.dto.LogQueryRequest;
import com.haitao.backend.entity.ActionLog;
import com.haitao.backend.entity.LoginLog;
import org.springframework.util.StringUtils;

/**
 * 日志查询工具类
 * 专门用于构建日志查询的QueryWrapper
 */
public class LogQueryUtil {

    /**
     * 构建操作日志查询条件
     */
    public static QueryWrapper<ActionLog> buildActionLogQuery(LogQueryRequest request) {
        QueryWrapper<ActionLog> queryWrapper = new QueryWrapper<>();
        
        // 用户ID筛选
        if (request.getUserId() != null) {
            queryWrapper.eq("al.user_id", request.getUserId());
        }
        
        // 操作类型筛选
        if (StringUtils.hasText(request.getActionType())) {
            queryWrapper.eq("al.action_type", request.getActionType());
        }
        
        // IP地址筛选
        if (StringUtils.hasText(request.getIpAddress())) {
            queryWrapper.like("al.ip_address", request.getIpAddress());
        }
        
        // 时间范围筛选
        if (StringUtils.hasText(request.getStartTime())) {
            queryWrapper.ge("al.action_time", request.getStartTime());
        }
        if (StringUtils.hasText(request.getEndTime())) {
            queryWrapper.le("al.action_time", request.getEndTime());
        }
        
        // 关键词搜索（用户名或真实姓名）
        if (StringUtils.hasText(request.getKeyword())) {
            queryWrapper.and(w -> w.like("u.username", request.getKeyword())
                           .or()
                           .like("u.real_name", request.getKeyword()));
        }
        
        // 添加排序
        queryWrapper.orderByDesc("al.action_time");
        
        return queryWrapper;
    }

    /**
     * 构建登录日志查询条件
     */
    public static QueryWrapper<LoginLog> buildLoginLogQuery(LogQueryRequest request) {
        QueryWrapper<LoginLog> queryWrapper = new QueryWrapper<>();
        
        // 用户ID筛选
        if (request.getUserId() != null) {
            queryWrapper.eq("ll.user_id", request.getUserId());
        }
        
        // 登录结果筛选
        if (request.getLoginResult() != null) {
            queryWrapper.eq("ll.login_result", request.getLoginResult());
        }
        
        // IP地址筛选
        if (StringUtils.hasText(request.getIpAddress())) {
            queryWrapper.like("ll.ip_address", request.getIpAddress());
        }
        
        // 时间范围筛选
        if (StringUtils.hasText(request.getStartTime())) {
            queryWrapper.ge("ll.login_time", request.getStartTime());
        }
        if (StringUtils.hasText(request.getEndTime())) {
            queryWrapper.le("ll.login_time", request.getEndTime());
        }
        
        // 关键词搜索（用户名或真实姓名）
        if (StringUtils.hasText(request.getKeyword())) {
            queryWrapper.and(w -> w.like("u.username", request.getKeyword())
                           .or()
                           .like("u.real_name", request.getKeyword()));
        }
        
        // 添加排序
        queryWrapper.orderByDesc("ll.login_time");
        
        return queryWrapper;
    }
}
