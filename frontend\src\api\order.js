import request from '../utils/request'

export const orderApi = {
  // 获取我的订单列表
  getMyOrderList(params) {
    return request({
      url: '/user/orders',
      method: 'get',
      params
    })
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/user/orders/${orderId}`,
      method: 'get'
    })
  },

  // 创建订单
  createOrder(data) {
    return request({
      url: '/user/orders',
      method: 'post',
      data
    })
  },

  // 更新订单
  updateOrder(orderId, data) {
    return request({
      url: `/user/orders/${orderId}`,
      method: 'put',
      data
    })
  },

  // 删除订单
  deleteOrder(orderId) {
    return request({
      url: `/user/orders/${orderId}`,
      method: 'delete'
    })
  },

  // 取消订单
  cancelOrder(orderId) {
    return request({
      url: `/user/orders/${orderId}/cancel`,
      method: 'put'
    })
  }
}
