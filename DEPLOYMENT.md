# 订单管理系统部署指南

## 概述

本文档详细说明了如何在生产环境中部署订单管理系统，包括nginx配置、IP地址获取优化等关键配置。

## 系统架构

```
Internet → Nginx → Spring Boot Application → MySQL Database
```

## 1. 服务器环境准备

### 1.1 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / RHEL 8+
- **Java**: OpenJDK 11 或 Oracle JDK 11+
- **MySQL**: 8.0+
- **Nginx**: 1.18+
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘**: 最低 20GB，推荐 50GB+

### 1.2 安装必要软件

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-11-jdk mysql-server nginx

# CentOS/RHEL
sudo yum update
sudo yum install java-11-openjdk mysql-server nginx
```

## 2. 数据库配置

### 2.1 创建数据库和用户

```sql
-- 创建数据库
CREATE DATABASE task_order_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'order_admin'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON task_order_db.* TO 'order_admin'@'localhost';
FLUSH PRIVILEGES;
```

### 2.2 导入数据库结构

```bash
mysql -u order_admin -p task_order_db < backend/sql/task_order_db.sql
```

## 3. 后端应用部署

### 3.1 构建应用

```bash
cd backend
./mvnw clean package -DskipTests
```

### 3.2 配置生产环境配置文件

创建 `application-prod.yml`:

```yaml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  datasource:
    url: **************************************************************************************************************************
    username: order_admin
    password: your_strong_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    
logging:
  level:
    com.haitao.backend: INFO
  file:
    name: /var/log/order-admin/application.log
    
jwt:
  secret: your_jwt_secret_key_here_make_it_very_long_and_secure
  expiration: 86400000
```

### 3.3 创建系统服务

创建 `/etc/systemd/system/order-admin.service`:

```ini
[Unit]
Description=Order Admin Application
After=network.target mysql.service

[Service]
Type=simple
User=order-admin
Group=order-admin
WorkingDirectory=/opt/order-admin
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod /opt/order-admin/order-admin.jar
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 3.4 启动服务

```bash
# 创建用户和目录
sudo useradd -r -s /bin/false order-admin
sudo mkdir -p /opt/order-admin /var/log/order-admin
sudo chown order-admin:order-admin /opt/order-admin /var/log/order-admin

# 复制jar文件
sudo cp target/order-admin-*.jar /opt/order-admin/order-admin.jar
sudo chown order-admin:order-admin /opt/order-admin/order-admin.jar

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable order-admin
sudo systemctl start order-admin
```

## 4. 前端应用部署

### 4.1 构建前端应用

```bash
cd frontend
npm install
npm run build
```

### 4.2 部署静态文件

```bash
sudo mkdir -p /var/www/order-admin
sudo cp -r dist/* /var/www/order-admin/
sudo chown -R nginx:nginx /var/www/order-admin
```

## 5. Nginx配置

### 5.1 创建nginx配置文件

创建 `/etc/nginx/sites-available/order-admin`:

```nginx
# 上游后端服务器
upstream order_backend {
    server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS主配置
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 客户端配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 前端静态资源
    location / {
        root /var/www/order-admin;
        try_files $uri $uri/ /index.html;
        index index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # API代理 - 关键的IP传递配置
    location ~ ^/(api|admin)/ {
        proxy_pass http://order_backend;
        
        # 传递真实客户端IP的关键配置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 连接配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # HTTP版本
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://order_backend/actuator/health;
        access_log off;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}

# 日志格式 - 包含真实IP
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for" '
                'rt=$request_time uct="$upstream_connect_time" '
                'uht="$upstream_header_time" urt="$upstream_response_time"';

access_log /var/log/nginx/order-admin.access.log main;
error_log /var/log/nginx/order-admin.error.log warn;
```

### 5.2 启用配置

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/order-admin /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
```

## 6. 验证部署

### 6.1 验证应用启动

```bash
# 检查应用状态
sudo systemctl status order-admin

# 查看应用日志
sudo journalctl -u order-admin -f
```

### 6.2 验证nginx代理

```bash
# 检查nginx状态
sudo systemctl status nginx

# 测试nginx配置
sudo nginx -t
```

### 6.3 验证日志记录

登录系统后检查操作日志和登录日志是否正确记录了真实IP地址。

## 7. 监控和维护

### 7.1 日志监控

```bash
# 查看应用日志
sudo journalctl -u order-admin -f

# 查看nginx日志
sudo tail -f /var/log/nginx/order-admin.access.log
sudo tail -f /var/log/nginx/order-admin.error.log
```

### 7.2 性能监控

建议使用以下工具进行监控：
- **应用监控**: Spring Boot Actuator + Prometheus + Grafana
- **服务器监控**: Node Exporter + Prometheus + Grafana
- **日志分析**: ELK Stack (Elasticsearch + Logstash + Kibana)

## 8. 安全建议

### 8.1 防火墙配置

```bash
# 只开放必要端口
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw enable
```

### 8.2 SSL证书

建议使用 Let's Encrypt 免费SSL证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 8.3 定期更新

```bash
# 定期更新系统
sudo apt update && sudo apt upgrade

# 定期备份数据库
mysqldump -u order_admin -p task_order_db > backup_$(date +%Y%m%d).sql
```

## 9. 故障排查

### 9.1 常见问题

1. **IP地址获取不正确**
   - 检查nginx配置中的proxy_set_header设置
   - 验证X-Forwarded-For等头信息是否正确传递

2. **应用无法启动**
   - 检查数据库连接配置
   - 查看应用日志: `sudo journalctl -u order-admin`

3. **静态资源404**
   - 检查nginx配置中的root路径
   - 验证文件权限设置

### 9.2 性能优化

1. **数据库优化**
   - 添加适当的索引
   - 配置连接池参数

2. **nginx优化**
   - 启用gzip压缩
   - 配置适当的缓存策略

3. **JVM优化**
   - 调整堆内存大小
   - 配置GC参数

通过以上配置，你的订单管理系统将能够在生产环境中正确获取客户端真实IPv4地址，并提供稳定可靠的服务。
