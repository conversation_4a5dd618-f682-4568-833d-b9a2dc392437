package com.haitao.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.dto.ActionLogResponse;
import com.haitao.backend.dto.LogQueryRequest;

/**
 * 操作日志服务接口
 */
public interface ActionLogService {
    
    /**
     * 记录操作日志
     */
    void saveActionLog(Long userId, String actionType, Long targetId, String description, String ipAddress);
    
    /**
     * 分页查询操作日志
     */
    IPage<ActionLogResponse> getActionLogList(LogQueryRequest request);
}
