<template>
  <div class="user-management">
    <!-- 搜索和操作栏 -->
    <div class="search-container">
      <div class="search-left">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索用户名或姓名"
          clearable
          @keyup.enter="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="searchForm.role" placeholder="角色" clearable class="filter-select">
          <el-option label="管理员" :value="0" />
          <el-option label="普通成员" :value="1" />
        </el-select>
        <el-select v-model="searchForm.status" placeholder="状态" clearable class="filter-select">
          <el-option label="正常" :value="1" />
          <el-option label="停用" :value="0" />
        </el-select>
        <el-button type="primary" @click="handleSearch" class="search-btn">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="resetSearch" class="reset-btn">重置</el-button>
      </div>
      <div class="search-right">
        <el-button
          v-if="selectedUsers.length > 0"
          type="danger"
          @click="handleBatchDelete"
          class="batch-delete-btn"
        >
          <el-icon><Delete /></el-icon>
          批量删除 ({{ selectedUsers.length }})
        </el-button>
        <el-button @click="getUserList" :loading="loading" class="refresh-btn">
          刷新
        </el-button>
        <el-button type="primary" @click="handleCreate" class="create-btn">
          <el-icon><Plus /></el-icon>
          新建用户
        </el-button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
        empty-text="暂无数据"
        class="user-table"
        :table-layout="'fixed'"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="realName" label="真实姓名" min-width="120" />
        <el-table-column prop="roleName" label="角色" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.role === 0 ? 'danger' : 'primary'">
              {{ row.roleName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" min-width="140" />
        <el-table-column prop="email" label="邮箱" min-width="200" show-overflow-tooltip />
        <el-table-column prop="statusName" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" width="320" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button
                :type="row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(row)"
              >
                {{ row.status === 1 ? '停用' : '启用' }}
              </el-button>
              <el-button
                v-if="row.role === 1"
                type="info"
                size="small"
                @click="handleResetPasswordDialog(row)"
              >
                重置密码
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="userForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="userForm.username" 
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" :value="0" />
            <el-option label="普通成员" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item v-if="!isEdit" label="初始密码" prop="initialPassword">
          <el-input 
            v-model="userForm.initialPassword" 
            placeholder="留空则使用默认密码123456"
            show-password
          />
        </el-form-item>
        <el-form-item v-if="isEdit" label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      :title="passwordDialogTitle"
      width="400px"
      @close="resetPasswordForm"
    >
      <div v-if="currentUser" class="user-info-display">
        <p><strong>用户名：</strong>{{ currentUser.username }}</p>
        <p><strong>真实姓名：</strong>{{ currentUser.realName || '-' }}</p>
        <p><strong>角色：</strong>{{ currentUser.roleName }}</p>
      </div>

      <el-alert
        title="重置密码说明"
        type="info"
        :closable="false"
        show-icon
        class="reset-info"
      >
        <template #default>
          <p>• 只能重置普通用户的密码，管理员密码不可重置</p>
          <p>• 建议用户登录后立即修改密码</p>
        </template>
      </el-alert>

      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            placeholder="请输入新密码（至少6位）"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleResetPassword" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Delete } from '@element-plus/icons-vue'
import { userApi } from '../../api/user'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const userList = ref([])
const selectedUsers = ref([])
const dialogVisible = ref(false)
const passwordDialogVisible = ref(false)
const isEdit = ref(false)
const currentUserId = ref(null)
const currentUser = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  role: null,
  status: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 用户表单
const userForm = reactive({
  username: '',
  realName: '',
  role: null,
  phone: '',
  email: '',
  initialPassword: '',
  status: 1
})

// 密码表单
const passwordForm = reactive({
  newPassword: ''
})

// 表单引用
const formRef = ref()
const passwordFormRef = ref()

// 对话框标题
const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新建用户')

// 重置密码对话框标题
const passwordDialogTitle = computed(() => {
  if (currentUser.value) {
    const userName = currentUser.value.realName || currentUser.value.username
    return `重置用户密码 - ${userName} (${currentUser.value.roleName})`
  }
  return '重置用户密码'
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}



// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const response = await userApi.getUserList(params)

    if (response.code === 200) {
      userList.value = response.data.records || []
      // 如果后端返回的total为0但有数据，使用records的长度
      pagination.total = response.data.total || response.data.records?.length || 0
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getUserList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    role: null,
    status: null
  })
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  getUserList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.page = page
  getUserList()
}

// 新建用户
const handleCreate = () => {
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  isEdit.value = true
  currentUserId.value = row.id
  Object.assign(userForm, {
    username: row.username,
    realName: row.realName,
    role: row.role,
    phone: row.phone || '',
    email: row.email || '',
    status: row.status
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(userForm, {
    username: '',
    realName: '',
    role: null,
    phone: '',
    email: '',
    initialPassword: '',
    status: 1
  })
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      // 编辑用户
      const response = await userApi.updateUser(currentUserId.value, userForm)
      if (response.code === 200) {
        ElMessage.success('用户更新成功')
        dialogVisible.value = false
        getUserList()
      }
    } else {
      // 创建用户
      const response = await userApi.createUser(userForm)
      if (response.code === 200) {
        ElMessage.success('用户创建成功')
        dialogVisible.value = false
        getUserList()
      }
    }
  } catch (error) {
    // 表单验证失败或请求失败
  } finally {
    submitting.value = false
  }
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要${row.status === 1 ? '停用' : '启用'}用户 ${row.realName || row.username} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await userApi.toggleUserStatus(row.id)
    if (response.code === 200) {
      ElMessage.success('操作成功')
      getUserList()
    }
  } catch {
    // 用户取消
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${row.realName || row.username} 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await userApi.deleteUser(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getUserList()
    }
  } catch {
    // 用户取消
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 批量删除用户
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    const userNames = selectedUsers.value.map(user => user.realName || user.username).join('、')
    await ElMessageBox.confirm(
      `确定要删除以下用户吗？此操作不可恢复！\n\n${userNames}`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    const response = await userApi.batchDeleteUsers(userIds)

    if (response.code === 200) {
      ElMessage.success(`成功删除 ${selectedUsers.value.length} 个用户`)
      selectedUsers.value = []
      getUserList()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败，请稍后重试')
    }
  }
}

// 打开重置密码对话框
const handleResetPasswordDialog = (row) => {
  // 检查是否为管理员，管理员不能重置密码
  if (row.role === 0) {
    ElMessage.warning('不能重置管理员密码')
    return
  }

  currentUserId.value = row.id
  currentUser.value = row
  passwordForm.newPassword = ''
  passwordDialogVisible.value = true
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.newPassword = ''
  currentUser.value = null
  currentUserId.value = null
  passwordFormRef.value?.resetFields()
}

// 重置密码
const handleResetPassword = async () => {
  try {
    // 再次检查当前用户角色，确保不是管理员
    if (currentUser.value && currentUser.value.role === 0) {
      ElMessage.error('不能重置管理员密码')
      return
    }

    await passwordFormRef.value.validate()
    submitting.value = true

    const response = await userApi.resetPassword(currentUserId.value, passwordForm)
    if (response.code === 200) {
      ElMessage.success('密码重置成功')
      passwordDialogVisible.value = false
      passwordForm.newPassword = ''
      currentUser.value = null
      currentUserId.value = null
    }
  } catch (error) {
    // 表单验证失败或请求失败
  } finally {
    submitting.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  getUserList()
})
</script>

<style scoped>
.user-management {
  padding: 24px;
  min-height: 100vh;
  background: transparent;
}

/* 搜索容器样式 */
.search-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.search-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 140px;
}

.search-btn,
.reset-btn,
.refresh-btn,
.create-btn {
  height: 40px;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 14px;
}

/* 添加一些现代化的样式 */
.search-container:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.table-container:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

/* 表格容器样式 */
.table-container {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  padding: 0;
  overflow: hidden;
}

.user-table {
  width: 100%;
}

.pagination-container {
  margin-top: 0;
  text-align: right;
  padding: 20px 24px;
  background: #ffffff;
  border-top: 1px solid #f3f4f6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  background: #f9fafb !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  box-shadow: none !important;
  transition: all 0.3s ease !important;
}

:deep(.el-input__wrapper:hover) {
  border-color: #3b82f6 !important;
  background: #f3f4f6 !important;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6 !important;
  background: #ffffff !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-input__inner) {
  color: #374151 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #9ca3af !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #f9fafb !important;
}

:deep(.el-button--primary) {
  background: #3b82f6 !important;
  border: 1px solid #3b82f6 !important;
  border-radius: 8px !important;
  color: #ffffff !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

:deep(.el-button--primary:hover) {
  background: #2563eb !important;
  border-color: #2563eb !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

:deep(.el-button) {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
}

:deep(.el-button--default) {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

:deep(.el-button--default:hover) {
  background: #f9fafb !important;
  border-color: #9ca3af !important;
}

:deep(.el-table) {
  background: #ffffff !important;
  color: #374151 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  border: 1px solid #e5e7eb !important;
  width: 100% !important;
  table-layout: fixed !important;
}

:deep(.el-table th) {
  background: #f9fafb !important;
  color: #374151 !important;
  border-bottom: 1px solid #e5e7eb !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  padding: 12px 8px !important;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6 !important;
  color: #6b7280 !important;
  font-size: 14px !important;
  padding: 12px 8px !important;
}

:deep(.el-table tr:hover > td) {
  background: #f8fafc !important;
}

:deep(.el-table .el-table__empty-text) {
  color: #9ca3af !important;
}

:deep(.el-table .el-table__row) {
  transition: background-color 0.3s ease !important;
}

:deep(.el-table .el-table__body-wrapper) {
  width: 100% !important;
}

:deep(.el-table .el-table__header-wrapper) {
  width: 100% !important;
}

/* 确保表格完全填充容器 */
:deep(.el-table__body),
:deep(.el-table__header) {
  width: 100% !important;
  table-layout: fixed !important;
}

/* 操作列按钮间距 */
:deep(.el-table .el-button + .el-button) {
  margin-left: 8px !important;
}

/* 确保表格行完全填充 */
:deep(.el-table tr) {
  width: 100% !important;
}

:deep(.el-table td),
:deep(.el-table th) {
  border-right: none !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 最后一列不需要省略号 */
:deep(.el-table td:last-child),
:deep(.el-table th:last-child) {
  white-space: normal !important;
}

/* 表格内容居中对齐 */
:deep(.el-table .cell) {
  padding: 0 8px !important;
  line-height: 1.5 !important;
}

/* 修复表格宽度问题 */
:deep(.el-table--border) {
  border: none !important;
}

:deep(.el-table--border::after) {
  display: none !important;
}

:deep(.el-table::before) {
  display: none !important;
}

:deep(.el-pagination) {
  color: #374151 !important;
}

:deep(.el-pagination .el-pager li) {
  background: #ffffff !important;
  color: #374151 !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  margin: 0 2px !important;
  transition: all 0.3s ease !important;
}

:deep(.el-pagination .el-pager li:hover) {
  background: #f3f4f6 !important;
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

:deep(.el-pagination .el-pager li.is-active) {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  color: #374151 !important;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  background: #f3f4f6 !important;
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

:deep(.el-pagination .el-pagination__total),
:deep(.el-pagination .el-pagination__jump) {
  color: #6b7280 !important;
}

:deep(.el-dialog) {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

:deep(.el-dialog__header) {
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px 24px !important;
}

:deep(.el-dialog__title) {
  color: #374151 !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

:deep(.el-dialog__body) {
  padding: 24px !important;
}

:deep(.el-form-item__label) {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* 用户信息显示样式 */
.user-info-display {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.user-info-display p {
  margin: 8px 0;
  color: #374151;
  font-size: 14px;
}

.user-info-display strong {
  color: #1f2937;
  font-weight: 600;
}

/* 重置密码说明样式 */
.reset-info {
  margin: 16px 0;
}

.reset-info p {
  margin: 4px 0;
  font-size: 13px;
  color: #6b7280;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

:deep(.el-select-dropdown) {
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* 标签样式美化 */
:deep(.el-tag) {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

:deep(.el-tag--primary) {
  background: #dbeafe !important;
  border-color: #93c5fd !important;
  color: #1d4ed8 !important;
}

:deep(.el-tag--success) {
  background: #dcfce7 !important;
  border-color: #86efac !important;
  color: #166534 !important;
}

:deep(.el-tag--danger) {
  background: #fef2f2 !important;
  border-color: #fca5a5 !important;
  color: #dc2626 !important;
}

:deep(.el-tag--warning) {
  background: #fef3c7 !important;
  border-color: #fcd34d !important;
  color: #d97706 !important;
}

/* 小按钮样式 */
:deep(.el-button--small) {
  padding: 6px 12px !important;
  font-size: 12px !important;
  border-radius: 6px !important;
}

:deep(.el-button--warning) {
  background: #f59e0b !important;
  border-color: #f59e0b !important;
  color: #ffffff !important;
}

:deep(.el-button--warning:hover) {
  background: #d97706 !important;
  border-color: #d97706 !important;
}

:deep(.el-button--danger) {
  background: #ef4444 !important;
  border-color: #ef4444 !important;
  color: #ffffff !important;
}

:deep(.el-button--danger:hover) {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
}

:deep(.el-button--success) {
  background: #10b981 !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
}

:deep(.el-button--success:hover) {
  background: #059669 !important;
  border-color: #059669 !important;
}

/* 批量删除按钮样式 */
.batch-delete-btn {
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.batch-delete-btn .el-icon {
  margin-right: 4px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}
</style>
