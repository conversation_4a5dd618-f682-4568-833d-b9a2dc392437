package com.haitao.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.annotation.ActionLog;
import com.haitao.backend.annotation.RequireAuth;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.Result;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.dto.*;
import com.haitao.backend.service.UserOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户订单管理控制器
 */
@RestController
@RequestMapping("/user/orders")
@RequireAuth("订单管理需要登录")
public class UserOrderController {
    
    @Autowired
    private UserOrderService userOrderService;
    
    /**
     * 获取我的订单列表
     */
    @GetMapping
    public Result<IPage<OrderTask>> getMyOrderList(TaskQueryRequest request,
                                                  HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        IPage<OrderTask> orders = userOrderService.getMyOrderList(request, userId);
        return ApiResponse.success("获取订单列表成功", orders);
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}")
    public Result<OrderTask> getOrderDetail(@PathVariable Long orderId,
                                           HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        OrderTask order = userOrderService.getMyOrderDetail(orderId, userId);
        return ApiResponse.success("获取订单详情成功", order);
    }
    
    /**
     * 创建订单
     */
    @PostMapping
    @ActionLog(actionType = "CREATE_ORDER", description = "创建订单：#{#arg0.projectName}", targetId = "#result.data.id")
    public Result<OrderTask> createOrder(@RequestBody CreateTaskRequest request,
                                        HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        OrderTask order = userOrderService.createOrder(request, userId);
        return ApiResponse.success("订单创建成功", order);
    }
    
    /**
     * 更新订单
     */
    @PutMapping("/{orderId}")
    @ActionLog(actionType = "UPDATE_ORDER", description = "更新订单：#{#arg1.projectName}", targetId = "#arg0")
    public Result<OrderTask> updateOrder(@PathVariable Long orderId,
                                        @RequestBody UpdateTaskRequest request,
                                        HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        OrderTask order = userOrderService.updateOrder(orderId, request, userId);
        return ApiResponse.success("订单更新成功", order);
    }
    
    /**
     * 删除订单
     */
    @DeleteMapping("/{orderId}")
    @ActionLog(actionType = "DELETE_ORDER", description = "删除订单", targetId = "#arg0")
    public Result<Void> deleteOrder(@PathVariable Long orderId,
                                   HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        userOrderService.deleteOrder(orderId, userId);
        return ApiResponse.success("订单删除成功");
    }
    
    /**
     * 取消订单
     */
    @PutMapping("/{orderId}/cancel")
    @ActionLog(actionType = "CANCEL_ORDER", description = "取消订单", targetId = "#arg0")
    public Result<OrderTask> cancelOrder(@PathVariable Long orderId,
                                        HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        OrderTask order = userOrderService.cancelOrder(orderId, userId);
        return ApiResponse.success("订单取消成功", order);
    }
}
