import request from '../utils/request'

export const userApi = {
  // 获取用户列表
  getUserList(params) {
    return request({
      url: '/admin/users',
      method: 'get',
      params
    })
  },

  // 创建用户
  createUser(data) {
    return request({
      url: '/admin/users',
      method: 'post',
      data
    })
  },

  // 更新用户信息
  updateUser(userId, data) {
    return request({
      url: `/admin/users/${userId}`,
      method: 'put',
      data
    })
  },

  // 删除用户
  deleteUser(userId) {
    return request({
      url: `/admin/users/${userId}`,
      method: 'delete'
    })
  },

  // 批量删除用户
  batchDeleteUsers(userIds) {
    return request({
      url: '/admin/users/batch',
      method: 'delete',
      data: userIds
    })
  },

  // 重置用户密码
  resetPassword(userId, data) {
    return request({
      url: `/admin/users/${userId}/reset-password`,
      method: 'post',
      data
    })
  },

  // 切换用户状态
  toggleUserStatus(userId) {
    return request({
      url: `/admin/users/${userId}/toggle-status`,
      method: 'post'
    })
  },

  // 获取用户详情
  getUserDetail(userId) {
    return request({
      url: `/admin/users/${userId}`,
      method: 'get'
    })
  }
}
