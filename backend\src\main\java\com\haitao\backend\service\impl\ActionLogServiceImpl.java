package com.haitao.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.dto.ActionLogResponse;
import com.haitao.backend.dto.LogQueryRequest;
import com.haitao.backend.entity.ActionLog;
import com.haitao.backend.mapper.ActionLogMapper;
import com.haitao.backend.service.ActionLogService;
import com.haitao.backend.util.LogQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 操作日志服务实现
 */
@Service
public class ActionLogServiceImpl implements ActionLogService {
    
    @Autowired
    private ActionLogMapper actionLogMapper;
    
    @Override
    public void saveActionLog(Long userId, String actionType, Long targetId, String description, String ipAddress) {
        ActionLog actionLog = new ActionLog(userId, actionType, targetId, description, ipAddress);
        actionLogMapper.insert(actionLog);
    }
    
    @Override
    public IPage<ActionLogResponse> getActionLogList(LogQueryRequest request) {
        Page<ActionLogResponse> page = new Page<>(request.getPage(), request.getSize());
        QueryWrapper<ActionLog> queryWrapper = LogQueryUtil.buildActionLogQuery(request);
        return actionLogMapper.selectActionLogWithUserInfo(page, queryWrapper);
    }
}
