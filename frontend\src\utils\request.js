import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 返回响应数据
    return response.data
  },
  error => {
    if (error.response?.status === 401) {
      // token过期，使用动态导入避免循环依赖
      import('../stores/user').then(({ useUserStore }) => {
        const userStore = useUserStore()
        userStore.logout()
        window.location.href = '/login'
      })
    } else {
      // 显示错误消息
      const message = error.response?.data?.message || error.message || '请求失败'
      ElMessage.error(message)
    }
    return Promise.reject(error)
  }
)

export default request
