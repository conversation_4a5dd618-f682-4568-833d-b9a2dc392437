import axios from 'axios'
import { ElMessage } from 'element-plus'

// 获取环境变量
const isDevelopment = import.meta.env.MODE === 'development'
const isProduction = import.meta.env.MODE === 'production'

// API基础配置
const API_CONFIG = {
  baseURL: isDevelopment ? '/api' : (import.meta.env.VITE_API_BASE_URL || '/api'),
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  withCredentials: false
}

// 创建axios实例
const request = axios.create(API_CONFIG)

// 开发环境日志
if (isDevelopment && import.meta.env.VITE_DEBUG === 'true') {
  console.log('API配置:', API_CONFIG)
}

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加时间戳防止缓存（仅在开发环境）
    if (isDevelopment) {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 开发环境请求日志
    if (isDevelopment && import.meta.env.VITE_DEBUG === 'true') {
      console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    }

    return config
  },
  error => {
    if (isDevelopment) {
      console.error('请求错误:', error)
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 开发环境响应日志
    if (isDevelopment && import.meta.env.VITE_DEBUG === 'true') {
      console.log('响应数据:', response.config.url, response.data)
    }

    // 返回响应数据
    return response.data
  },
  error => {
    // 开发环境错误日志
    if (isDevelopment) {
      console.error('响应错误:', error.response?.status, error.response?.data || error.message)
    }

    // 处理不同的错误状态
    if (error.response?.status === 401) {
      // token过期，使用动态导入避免循环依赖
      import('../stores/user').then(({ useUserStore }) => {
        const userStore = useUserStore()
        userStore.logout()
        window.location.href = '/login'
      })
    } else if (error.response?.status === 403) {
      ElMessage.error('权限不足，请联系管理员')
    } else if (error.response?.status === 404) {
      ElMessage.error('请求的资源不存在')
    } else if (error.response?.status >= 500) {
      ElMessage.error('服务器内部错误，请稍后重试')
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.code === 'ERR_NETWORK') {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      // 显示错误消息
      const message = error.response?.data?.message || error.message || '请求失败'
      ElMessage.error(message)
    }

    return Promise.reject(error)
  }
)

export default request
