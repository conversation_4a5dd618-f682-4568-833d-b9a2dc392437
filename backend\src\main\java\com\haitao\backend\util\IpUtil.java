package com.haitao.backend.util;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 * 支持nginx代理环境下的真实IPv4地址获取
 */
public class IpUtil {

    /**
     * IPv4地址正则表达式
     */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
        "^(([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}([01]?\\d\\d?|2[0-4]\\d|25[0-5])$"
    );

    /**
     * 内网IP地址正则表达式
     */
    private static final Pattern INTERNAL_IP_PATTERN = Pattern.compile(
        "^(127\\..*|10\\..*|172\\.1[6-9]\\..*|172\\.2[0-9]\\..*|172\\.3[0-1]\\..*|192\\.168\\..*)$"
    );

    /**
     * 未知IP标识
     */
    private static final String UNKNOWN = "unknown";

    /**
     * 本地IP地址
     */
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IPv4地址
     * 适用于nginx代理环境
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return LOCALHOST_IPV4;
        }

        // 按优先级顺序检查各种代理头
        String[] headers = {
            "X-Forwarded-For",      // nginx代理常用
            "X-Real-IP",            // nginx代理常用
            "CF-Connecting-IP",     // Cloudflare
            "X-Cluster-Client-IP",  // 集群代理
            "Proxy-Client-IP",      // 代理服务器
            "WL-Proxy-Client-IP",   // WebLogic代理
            "HTTP_CLIENT_IP",       // HTTP代理
            "HTTP_X_FORWARDED_FOR", // HTTP转发
            "HTTP_X_REAL_IP"        // HTTP真实IP
        };

        String ip = null;

        // 遍历所有可能的头信息
        for (String header : headers) {
            ip = request.getHeader(header);
            if (isValidIp(ip)) {
                // 处理多级代理的情况（X-Forwarded-For可能包含多个IP）
                if (ip.contains(",")) {
                    String[] ips = ip.split(",");
                    for (String singleIp : ips) {
                        singleIp = singleIp.trim();
                        if (isValidIp(singleIp) && isIPv4(singleIp)) {
                            return singleIp;
                        }
                    }
                } else if (isIPv4(ip)) {
                    return ip;
                }
            }
        }

        // 如果所有代理头都没有获取到有效IP，使用request.getRemoteAddr()
        ip = request.getRemoteAddr();

        // 处理IPv6本地地址
        if (LOCALHOST_IPV6.equals(ip)) {
            ip = LOCALHOST_IPV4;
        }

        // 如果获取到的是内网IP，尝试获取本机的外网IP
        if (isInternalIp(ip)) {
            try {
                InetAddress inetAddress = InetAddress.getLocalHost();
                String hostAddress = inetAddress.getHostAddress();
                if (isIPv4(hostAddress) && !isInternalIp(hostAddress)) {
                    return hostAddress;
                }
            } catch (UnknownHostException e) {
                // 忽略异常，使用原IP
            }
        }

        return isIPv4(ip) ? ip : LOCALHOST_IPV4;
    }

    /**
     * 验证IP地址是否有效
     */
    private static boolean isValidIp(String ip) {
        return ip != null
            && ip.length() > 0
            && !UNKNOWN.equalsIgnoreCase(ip)
            && !"null".equalsIgnoreCase(ip);
    }

    /**
     * 判断是否为IPv4地址
     */
    private static boolean isIPv4(String ip) {
        return ip != null && IPV4_PATTERN.matcher(ip).matches();
    }

    /**
     * 判断是否为内网IP
     */
    private static boolean isInternalIp(String ip) {
        return ip != null && INTERNAL_IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 获取服务器IP地址
     */
    public static String getServerIpAddress() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            return LOCALHOST_IPV4;
        }
    }

    /**
     * 判断IP地址是否为本地地址
     */
    public static boolean isLocalhost(String ip) {
        return LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip) || "localhost".equalsIgnoreCase(ip);
    }
}
