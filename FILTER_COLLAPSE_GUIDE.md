# 筛选卡片折叠功能指南

## 🎯 功能概述

为收入统计模块的筛选卡片添加了折叠功能，提升页面空间利用率和用户体验。

## ✨ 主要特性

### 1. 智能折叠
- **一键折叠**: 点击箭头图标即可折叠/展开筛选区域
- **状态记忆**: 自动保存折叠状态到本地存储
- **平滑动画**: 使用Element Plus的折叠过渡动画

### 2. 折叠状态优化
- **筛选摘要**: 折叠时显示当前筛选条件的简要信息
- **快速操作**: 折叠状态下仍可进行快速时间选择和查询
- **视觉反馈**: 箭头图标旋转动画指示折叠状态

### 3. 响应式设计
- **移动端适配**: 在小屏幕设备上优化布局
- **灵活布局**: 折叠状态下的按钮自动换行
- **触摸友好**: 适合触摸操作的按钮尺寸

## 🛠️ 技术实现

### 1. 状态管理
```javascript
// 折叠状态
const filterCollapsed = ref(false)

// 切换折叠状态
function toggleFilterCollapse() {
  filterCollapsed.value = !filterCollapsed.value
  
  // 保存到本地存储
  localStorage.setItem('income-statistics-filter-collapsed', filterCollapsed.value.toString())
}
```

### 2. 本地存储
```javascript
// 页面初始化时恢复状态
async function initPage() {
  const savedCollapsed = localStorage.getItem('income-statistics-filter-collapsed')
  if (savedCollapsed !== null) {
    filterCollapsed.value = savedCollapsed === 'true'
  }
  // ...其他初始化逻辑
}
```

### 3. 筛选摘要生成
```javascript
// 生成筛选条件摘要
function getFilterSummary() {
  const summary = []
  
  // 时间维度
  const timeDimensionMap = {
    year: '按年',
    month: '按月', 
    week: '按周',
    day: '按日'
  }
  summary.push(timeDimensionMap[queryForm.timeDimension] || '按月')
  
  // 时间范围
  if (queryForm.startTime && queryForm.endTime) {
    const start = new Date(queryForm.startTime).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    const end = new Date(queryForm.endTime).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    summary.push(`${start} - ${end}`)
  }
  
  // 用户筛选（仅管理员页面）
  if (queryForm.userId && userList.value.length > 0) {
    const user = userList.value.find(u => u.id === queryForm.userId)
    if (user) {
      summary.push(user.realName)
    }
  }
  
  // 统计范围
  summary.push(queryForm.onlySettled ? '仅已结算' : '已完成+已结算')
  
  return summary.join(' | ')
}
```

## 🎨 UI设计

### 1. 折叠按钮
```vue
<el-button 
  @click="toggleFilterCollapse" 
  size="small" 
  text
  class="collapse-btn"
  :title="filterCollapsed ? '展开筛选' : '折叠筛选'"
>
  <el-icon class="collapse-icon" :class="{ 'collapsed': filterCollapsed }">
    <ArrowDown />
  </el-icon>
</el-button>
```

### 2. 筛选摘要标签
```vue
<el-tag v-if="filterCollapsed" type="info" size="small" class="filter-summary">
  {{ getFilterSummary() }}
</el-tag>
```

### 3. 折叠动画
```vue
<el-collapse-transition>
  <div v-show="!filterCollapsed" class="filter-content">
    <!-- 筛选内容 -->
  </div>
</el-collapse-transition>
```

## 🎯 样式设计

### 1. 折叠按钮样式
```scss
.collapse-btn {
  padding: 4px 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f0f0f0; // 管理员页面
    background-color: rgba(102, 126, 234, 0.1); // 个人页面
    border-radius: 6px;
  }
}

.collapse-icon {
  transition: transform 0.3s ease;
  
  &.collapsed {
    transform: rotate(-90deg);
  }
}
```

### 2. 折叠状态下的快速操作
```scss
.collapsed-quick-actions {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.quick-btn-collapsed {
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}
```

### 3. 筛选摘要样式
```scss
.filter-summary {
  margin-left: 12px;
  font-weight: normal;
  font-size: 12px;
}
```

## 📱 响应式适配

### 1. 移动端优化
```scss
@media (max-width: 768px) {
  .collapsed-quick-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .quick-buttons-collapsed {
    justify-content: center;
  }
  
  .collapsed-main-actions {
    justify-content: center;
  }
}
```

### 2. 触摸友好设计
- 按钮最小尺寸44px
- 适当的间距和内边距
- 清晰的视觉反馈

## 🔧 使用方法

### 1. 基本操作
1. **折叠筛选**: 点击筛选卡片标题右侧的箭头按钮
2. **查看摘要**: 折叠状态下可在标题栏看到当前筛选条件
3. **快速操作**: 折叠状态下仍可使用快速时间选择和查询按钮
4. **展开筛选**: 再次点击箭头按钮展开完整筛选界面

### 2. 状态保持
- 折叠状态会自动保存到浏览器本地存储
- 下次访问页面时会自动恢复上次的折叠状态
- 不同页面的折叠状态独立保存

### 3. 快速筛选
折叠状态下提供的快速操作：
- **时间快选**: 最近一周、一月、三月、本年度
- **立即查询**: 应用当前筛选条件
- **重置筛选**: 恢复默认筛选条件

## 🎯 用户体验提升

### 1. 空间优化
- **节省空间**: 折叠后可节省约60%的筛选区域空间
- **聚焦内容**: 更多空间展示统计图表和数据
- **减少滚动**: 特别是在移动端设备上

### 2. 操作便利
- **一键切换**: 简单的点击操作
- **状态记忆**: 无需重复设置
- **快速访问**: 常用功能在折叠状态下仍可使用

### 3. 视觉清晰
- **状态指示**: 箭头动画清晰指示折叠状态
- **信息摘要**: 折叠时仍能了解当前筛选条件
- **平滑过渡**: 优雅的折叠展开动画

## 🔮 未来扩展

### 1. 功能增强
- [ ] 添加键盘快捷键支持（如Ctrl+F折叠筛选）
- [ ] 支持拖拽调整筛选区域高度
- [ ] 添加筛选历史记录功能
- [ ] 支持自定义筛选摘要格式

### 2. 交互优化
- [ ] 添加折叠状态下的悬浮提示
- [ ] 支持双击标题栏快速折叠
- [ ] 添加折叠状态的音效反馈
- [ ] 优化动画性能

### 3. 个性化设置
- [ ] 允许用户自定义默认折叠状态
- [ ] 支持不同页面的独立设置
- [ ] 添加筛选区域主题切换
- [ ] 支持筛选条件的快速预设

通过这个折叠功能，用户可以更灵活地管理页面空间，在需要时展开详细筛选，在查看数据时折叠筛选区域，大大提升了使用体验。
