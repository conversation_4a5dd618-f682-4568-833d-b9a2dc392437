package com.haitao.backend.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.function.Consumer;

/**
 * MyBatis-Plus查询工具类
 * 提供常用的Lambda查询构造器方法
 */
public class QueryUtil {

    /**
     * 创建基础的Lambda查询构造器
     */
    public static <T> LambdaQueryWrapper<T> lambdaQuery() {
        return new LambdaQueryWrapper<>();
    }

    /**
     * 创建带条件的Lambda查询构造器
     */
    public static <T> LambdaQueryWrapper<T> lambdaQuery(Class<T> entityClass) {
        return new LambdaQueryWrapper<>();
    }

    /**
     * 字符串相等条件（非空时生效）
     */
    public static <T> LambdaQueryWrapper<T> eqIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        String value) {
        return wrapper.eq(StringUtils.hasText(value), column, value);
    }

    /**
     * 数值相等条件（非空时生效）
     */
    public static <T> LambdaQueryWrapper<T> eqIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        Object value) {
        return wrapper.eq(value != null, column, value);
    }

    /**
     * 模糊查询条件（非空时生效）
     */
    public static <T> LambdaQueryWrapper<T> likeIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                          SFunction<T, ?> column, 
                                                          String value) {
        return wrapper.like(StringUtils.hasText(value), column, value);
    }

    /**
     * 时间范围查询（开始时间）
     */
    public static <T> LambdaQueryWrapper<T> geIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        LocalDateTime startTime) {
        return wrapper.ge(startTime != null, column, startTime);
    }

    /**
     * 时间范围查询（结束时间）
     */
    public static <T> LambdaQueryWrapper<T> leIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        LocalDateTime endTime) {
        return wrapper.le(endTime != null, column, endTime);
    }

    /**
     * 字符串时间范围查询（开始时间）
     */
    public static <T> LambdaQueryWrapper<T> geIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        String startTime) {
        return wrapper.ge(StringUtils.hasText(startTime), column, startTime);
    }

    /**
     * 字符串时间范围查询（结束时间）
     */
    public static <T> LambdaQueryWrapper<T> leIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        String endTime) {
        return wrapper.le(StringUtils.hasText(endTime), column, endTime);
    }

    /**
     * IN查询条件（集合非空时生效）
     */
    public static <T> LambdaQueryWrapper<T> inIfPresent(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?> column, 
                                                        Collection<?> values) {
        return wrapper.in(values != null && !values.isEmpty(), column, values);
    }

    /**
     * 多字段关键词搜索
     */
    public static <T> LambdaQueryWrapper<T> keywordSearch(LambdaQueryWrapper<T> wrapper,
                                                          String keyword,
                                                          SFunction<T, ?>... columns) {
        if (StringUtils.hasText(keyword) && columns.length > 0) {
            wrapper.and(w -> {
                w.like(columns[0], keyword);
                for (int i = 1; i < columns.length; i++) {
                    w.or().like(columns[i], keyword);
                }
            });
        }
        return wrapper;
    }

    /**
     * 构建分页查询条件的通用方法
     */
    public static <T> LambdaQueryWrapper<T> buildPageQuery(Class<T> entityClass, 
                                                           Consumer<LambdaQueryWrapper<T>> conditions) {
        LambdaQueryWrapper<T> wrapper = new LambdaQueryWrapper<>();
        if (conditions != null) {
            conditions.accept(wrapper);
        }
        return wrapper;
    }

    /**
     * 构建排序条件
     */
    public static <T> LambdaQueryWrapper<T> orderByDesc(LambdaQueryWrapper<T> wrapper, 
                                                        SFunction<T, ?>... columns) {
        for (SFunction<T, ?> column : columns) {
            wrapper.orderByDesc(column);
        }
        return wrapper;
    }

    /**
     * 构建排序条件
     */
    public static <T> LambdaQueryWrapper<T> orderByAsc(LambdaQueryWrapper<T> wrapper, 
                                                       SFunction<T, ?>... columns) {
        for (SFunction<T, ?> column : columns) {
            wrapper.orderByAsc(column);
        }
        return wrapper;
    }

    /**
     * 链式调用示例方法
     */
    public static class Builder<T> {
        private final LambdaQueryWrapper<T> wrapper;

        public Builder() {
            this.wrapper = new LambdaQueryWrapper<>();
        }

        public Builder<T> eq(boolean condition, SFunction<T, ?> column, Object value) {
            wrapper.eq(condition, column, value);
            return this;
        }

        public Builder<T> like(boolean condition, SFunction<T, ?> column, Object value) {
            wrapper.like(condition, column, value);
            return this;
        }

        public Builder<T> ge(boolean condition, SFunction<T, ?> column, Object value) {
            wrapper.ge(condition, column, value);
            return this;
        }

        public Builder<T> le(boolean condition, SFunction<T, ?> column, Object value) {
            wrapper.le(condition, column, value);
            return this;
        }

        public Builder<T> orderByDesc(SFunction<T, ?> column) {
            wrapper.orderByDesc(column);
            return this;
        }

        public Builder<T> orderByAsc(SFunction<T, ?> column) {
            wrapper.orderByAsc(column);
            return this;
        }

        public Builder<T> and(boolean condition, Consumer<LambdaQueryWrapper<T>> consumer) {
            if (condition) {
                wrapper.and(consumer);
            }
            return this;
        }

        public Builder<T> and(Consumer<LambdaQueryWrapper<T>> consumer) {
            wrapper.and(consumer);
            return this;
        }

        public LambdaQueryWrapper<T> build() {
            return wrapper;
        }
    }

    /**
     * 创建Builder实例
     */
    public static <T> Builder<T> builder() {
        return new Builder<>();
    }
}
