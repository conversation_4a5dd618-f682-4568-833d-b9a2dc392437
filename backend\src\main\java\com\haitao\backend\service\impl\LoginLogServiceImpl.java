package com.haitao.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.dto.LoginLogResponse;
import com.haitao.backend.dto.LogQueryRequest;
import com.haitao.backend.entity.LoginLog;
import com.haitao.backend.mapper.LoginLogMapper;
import com.haitao.backend.service.LoginLogService;
import com.haitao.backend.util.LogQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 登录日志服务实现
 */
@Service
public class LoginLogServiceImpl implements LoginLogService {
    
    @Autowired
    private LoginLogMapper loginLogMapper;
    
    @Override
    public void saveLoginLog(Long userId, String ipAddress, String userAgent, Integer loginResult) {
        LoginLog loginLog = new LoginLog(userId, ipAddress, userAgent, loginResult);
        loginLogMapper.insert(loginLog);
    }
    
    @Override
    public IPage<LoginLogResponse> getLoginLogList(LogQueryRequest request) {
        Page<LoginLogResponse> page = new Page<>(request.getPage(), request.getSize());
        QueryWrapper<LoginLog> queryWrapper = LogQueryUtil.buildLoginLogQuery(request);
        return loginLogMapper.selectLoginLogWithUserInfo(page, queryWrapper);
    }
}
