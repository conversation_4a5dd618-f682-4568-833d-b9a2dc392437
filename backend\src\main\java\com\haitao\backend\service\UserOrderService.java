package com.haitao.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.dto.CreateTaskRequest;
import com.haitao.backend.dto.UpdateTaskRequest;
import com.haitao.backend.dto.TaskQueryRequest;

/**
 * 用户订单服务接口
 */
public interface UserOrderService {
    
    /**
     * 获取我的订单列表
     */
    IPage<OrderTask> getMyOrderList(TaskQueryRequest request, Long userId);
    
    /**
     * 获取我的订单详情
     */
    OrderTask getMyOrderDetail(Long orderId, Long userId);
    
    /**
     * 创建订单
     */
    OrderTask createOrder(CreateTaskRequest request, Long userId);
    
    /**
     * 更新订单
     */
    OrderTask updateOrder(Long orderId, UpdateTaskRequest request, Long userId);
    
    /**
     * 删除订单
     */
    void deleteOrder(Long orderId, Long userId);
    
    /**
     * 取消订单
     */
    OrderTask cancelOrder(Long orderId, Long userId);
}
