package com.haitao.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.Result;
import com.haitao.backend.dto.ActionLogResponse;
import com.haitao.backend.dto.LogQueryRequest;
import com.haitao.backend.service.ActionLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作日志管理控制器
 */
@RestController
@RequestMapping("/admin/action-logs")
@RequireAdmin("操作日志查看需要管理员权限")
public class ActionLogController {
    
    @Autowired
    private ActionLogService actionLogService;
    
    /**
     * 分页查询操作日志
     */
    @GetMapping
    public Result<IPage<ActionLogResponse>> getActionLogList(LogQueryRequest request) {
        IPage<ActionLogResponse> logList = actionLogService.getActionLogList(request);
        return ApiResponse.success(logList);
    }
}
