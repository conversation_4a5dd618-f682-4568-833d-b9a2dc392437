package com.haitao.backend.dto;

/**
 * 日志查询请求DTO
 */
public class LogQueryRequest extends PageRequest {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 操作类型（仅操作日志）
     */
    private String actionType;
    
    /**
     * 登录结果（仅登录日志）
     */
    private Integer loginResult;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 关键词搜索（用户名或真实姓名）
     */
    private String keyword;
    
    // Getter和Setter方法
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getActionType() {
        return actionType;
    }
    
    public void setActionType(String actionType) {
        this.actionType = actionType;
    }
    
    public Integer getLoginResult() {
        return loginResult;
    }
    
    public void setLoginResult(Integer loginResult) {
        this.loginResult = loginResult;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
