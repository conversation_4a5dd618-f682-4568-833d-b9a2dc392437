package com.haitao.backend.common;

import lombok.Data;

/**
 * API响应包装器
 * 提供更简洁的API响应创建方式
 */
@Data
public class ApiResponse {
    
    /**
     * 成功响应（无数据）
     */
    public static Result<Void> success() {
        return Result.success();
    }
    
    /**
     * 成功响应（带消息）
     */
    public static Result<Void> success(String message) {
        return Result.success(message);
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        return Result.success(data);
    }
    
    /**
     * 成功响应（带消息和数据）
     */
    public static <T> Result<T> success(String message, T data) {
        return Result.success(message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> Result<T> error(String message) {
        return Result.error(message);
    }

    /**
     * 失败响应（自定义状态码）
     */
    public static <T> Result<T> error(Integer code, String message) {
        return Result.error(code, message);
    }
    
    /**
     * 参数错误响应
     */
    public static <T> Result<T> badRequest(String message) {
        return Result.badRequest(message);
    }

    /**
     * 未授权响应
     */
    public static <T> Result<T> unauthorized(String message) {
        return Result.unauthorized(message);
    }

    /**
     * 禁止访问响应
     */
    public static <T> Result<T> forbidden(String message) {
        return Result.forbidden(message);
    }
    
    /**
     * 资源不存在响应
     */
    public static <T> Result<T> notFound(String message) {
        return Result.error(ResponseCode.NOT_FOUND, message);
    }

    /**
     * 服务器内部错误响应
     */
    public static <T> Result<T> internalError(String message) {
        return Result.error(ResponseCode.INTERNAL_SERVER_ERROR, message);
    }

    /**
     * 业务错误响应
     */
    public static <T> Result<T> businessError(String message) {
        return Result.error(ResponseCode.BUSINESS_ERROR, message);
    }

    /**
     * 数据验证错误响应
     */
    public static <T> Result<T> validationError(String message) {
        return Result.error(ResponseCode.VALIDATION_ERROR, message);
    }

    /**
     * 数据重复错误响应
     */
    public static <T> Result<T> duplicateError(String message) {
        return Result.error(ResponseCode.DUPLICATE_ERROR, message);
    }
}
