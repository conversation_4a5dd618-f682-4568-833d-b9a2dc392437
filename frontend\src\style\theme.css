/* 海泡订单系统 - 全局主题样式 */

:root {
  /* 主题色彩 - 深海蓝色系 */
  --primary-color: #3b82f6;
  --primary-light: #60a5fa;
  --primary-dark: #1d4ed8;
  
  /* 背景渐变色 */
  --bg-gradient: linear-gradient(135deg, 
    #0c1e3d 0%, 
    #1e3a8a 25%, 
    #1e40af 50%, 
    #0369a1 75%, 
    #0284c7 100%);
  
  /* 深色背景 */
  --bg-dark: #0f172a;
  --bg-dark-light: rgba(15, 23, 42, 0.85);
  
  /* 边框和分割线 */
  --border-color: rgba(59, 130, 246, 0.3);
  --border-light: rgba(59, 130, 246, 0.2);
  
  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  
  /* 阴影效果 */
  --shadow-primary: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-secondary: 0 0 15px rgba(59, 130, 246, 0.2);
  --shadow-card: 0 20px 40px rgba(0, 0, 0, 0.3);
  
  /* 毛玻璃效果 */
  --glass-bg: rgba(59, 130, 246, 0.1);
  --glass-border: rgba(59, 130, 246, 0.2);
  --glass-blur: blur(10px);
  
  /* 动画时间 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-gradient);
  color: var(--text-primary);
  overflow-x: hidden;
}

/* 通用毛玻璃卡片样式 */
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  backdrop-filter: var(--glass-blur);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal) ease;
}

.glass-card:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: var(--shadow-primary);
  transform: translateY(-2px);
}

/* 通用按钮样式 */
.ocean-button {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.9) 0%, 
    rgba(14, 165, 233, 1) 50%, 
    rgba(6, 182, 212, 0.9) 100%);
  border: none;
  border-radius: 12px;
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal) ease;
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.ocean-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%);
  transition: left 0.6s ease;
}

.ocean-button:hover::before {
  left: 100%;
}

.ocean-button:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 12px 35px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.ocean-button:active {
  transform: translateY(-1px);
}

/* 通用输入框样式 */
.ocean-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(30, 58, 138, 0.15);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 0 20px;
  height: 55px;
  backdrop-filter: var(--glass-blur);
  transition: all var(--transition-normal) ease;
  overflow: hidden;
}

.ocean-input-container:hover {
  border-color: var(--border-color);
  background: rgba(30, 58, 138, 0.2);
  box-shadow: var(--shadow-secondary);
}

.ocean-input-container:focus-within {
  border-color: var(--primary-color);
  background: rgba(30, 58, 138, 0.25);
  box-shadow: var(--shadow-primary);
}

/* 通用文字发光效果 */
.text-glow {
  text-shadow: 
    0 0 10px var(--primary-color),
    0 0 20px rgba(59, 130, 246, 0.6),
    0 0 30px rgba(59, 130, 246, 0.4);
}

/* 通用动画类 */
.fade-in {
  animation: fadeIn var(--transition-slow) ease-in-out;
}

.slide-up {
  animation: slideUp var(--transition-slow) ease-out;
}

.scale-in {
  animation: scaleIn var(--transition-normal) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
  transition: background var(--transition-normal) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

/* Element Plus 组件样式覆盖 */
.el-header {
  padding: 0 !important;
}

.el-main {
  padding: 0 !important;
}

/* 响应式断点 */
@media (max-width: 768px) {
  :root {
    --shadow-primary: 0 0 15px rgba(59, 130, 246, 0.25);
    --shadow-secondary: 0 0 10px rgba(59, 130, 246, 0.15);
  }
  
  .glass-card {
    border-radius: 12px;
  }
  
  .ocean-button {
    border-radius: 10px;
  }
  
  .ocean-input-container {
    border-radius: 10px;
    height: 50px;
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .glass-card {
    border-radius: 10px;
  }
  
  .ocean-button {
    border-radius: 8px;
  }
  
  .ocean-input-container {
    border-radius: 8px;
    height: 48px;
    padding: 0 14px;
  }
}
