package com.haitao.backend.dto;

import lombok.Data;
import java.util.List;

/**
 * 分页响应DTO
 */
@Data
public class PageResponse<T> {
    
    private List<T> records; // 数据列表
    
    private Long total; // 总记录数
    
    private Integer page; // 当前页码
    
    private Integer size; // 每页大小
    
    private Integer pages; // 总页数
    
    public PageResponse() {}
    
    public PageResponse(List<T> records, Long total, Integer page, Integer size) {
        this.records = records;
        this.total = total;
        this.page = page;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
}
