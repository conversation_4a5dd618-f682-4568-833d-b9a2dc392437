package com.haitao.backend.aspect;

import com.haitao.backend.annotation.ActionLog;
import com.haitao.backend.service.ActionLogService;
import com.haitao.backend.util.IpUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 操作日志切面
 */
@Aspect
@Component
public class ActionLogAspect {
    
    @Autowired
    private ActionLogService actionLogService;
    
    private final ExpressionParser parser = new SpelExpressionParser();
    
    @AfterReturning(pointcut = "@annotation(actionLog)", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, ActionLog actionLog, Object result) {
        try {
            // 获取当前请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }
            
            HttpServletRequest request = attributes.getRequest();
            
            // 获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return;
            }
            
            // 获取IP地址
            String ipAddress = IpUtil.getClientIpAddress(request);
            
            // 创建SpEL上下文
            EvaluationContext context = new StandardEvaluationContext();
            
            // 设置方法参数
            Object[] args = joinPoint.getArgs();
            String[] paramNames = getParameterNames(joinPoint);
            for (int i = 0; i < args.length; i++) {
                if (i < paramNames.length) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }
            
            // 设置返回结果
            context.setVariable("result", result);
            context.setVariable("request", request);
            
            // 解析描述信息
            String description = parseExpression(actionLog.description(), context);
            
            // 解析目标ID
            Long targetId = null;
            if (!actionLog.targetId().isEmpty()) {
                Object targetIdObj = parseExpression(actionLog.targetId(), context);
                if (targetIdObj instanceof Number) {
                    targetId = ((Number) targetIdObj).longValue();
                }
            }
            
            // 保存操作日志
            actionLogService.saveActionLog(userId, actionLog.actionType(), targetId, description, ipAddress);
            
        } catch (Exception e) {
            // 日志记录失败不应该影响业务流程
            e.printStackTrace();
        }
    }
    
    /**
     * 解析SpEL表达式
     */
    private String parseExpression(String expressionString, EvaluationContext context) {
        try {
            Expression expression = parser.parseExpression(expressionString);
            Object value = expression.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            return expressionString; // 解析失败返回原字符串
        }
    }
    
    /**
     * 获取方法参数名（简化版本）
     */
    private String[] getParameterNames(JoinPoint joinPoint) {
        // 这里简化处理，实际项目中可以使用反射或其他方式获取真实参数名
        Object[] args = joinPoint.getArgs();
        String[] names = new String[args.length];
        for (int i = 0; i < args.length; i++) {
            names[i] = "arg" + i;
        }
        return names;
    }
}
