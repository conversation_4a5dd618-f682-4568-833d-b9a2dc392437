package com.haitao.backend.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 请求响应日志过滤器
 */
@Component
public class RequestResponseLoggingFilter extends OncePerRequestFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(RequestResponseLoggingFilter.class);
    
    // 不记录详细日志的路径
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
        "/favicon.ico",
        "/error"
    );
    
    // 不记录请求体的Content-Type
    private static final List<String> EXCLUDE_CONTENT_TYPES = Arrays.asList(
        "multipart/form-data",
        "application/octet-stream"
    );
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {
        
        String requestURI = request.getRequestURI();
        
        // 检查是否需要记录详细日志
        if (shouldSkipLogging(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 包装请求和响应以便读取内容
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);
        
        try {
            // 记录请求详情
            logRequestDetails(wrappedRequest);
            
            // 继续过滤链
            filterChain.doFilter(wrappedRequest, wrappedResponse);
            
            // 记录响应详情
            logResponseDetails(wrappedRequest, wrappedResponse);
            
        } finally {
            // 重要：将响应内容写回客户端
            wrappedResponse.copyBodyToResponse();
        }
    }
    
    /**
     * 记录请求详情
     */
    private void logRequestDetails(ContentCachingRequestWrapper request) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        String contentType = request.getContentType();
        
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("请求详情: ").append(method).append(" ").append(uri);
        
        if (queryString != null) {
            logMessage.append("?").append(queryString);
        }
        
        // 记录请求头（部分）
        logMessage.append(" | Content-Type: ").append(contentType);
        logMessage.append(" | Content-Length: ").append(request.getContentLength());
        
        // 记录请求体（如果适合）
        if (shouldLogRequestBody(contentType)) {
            byte[] content = request.getContentAsByteArray();
            if (content.length > 0 && content.length < 1024) { // 限制大小
                String requestBody = new String(content, StandardCharsets.UTF_8);
                logMessage.append(" | 请求体: ").append(requestBody);
            }
        }
        
        logger.debug(logMessage.toString());
    }
    
    /**
     * 记录响应详情
     */
    private void logResponseDetails(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        int status = response.getStatus();
        String contentType = response.getContentType();
        
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("响应详情: ").append(method).append(" ").append(uri);
        logMessage.append(" | 状态: ").append(status);
        logMessage.append(" | Content-Type: ").append(contentType);
        
        // 记录响应体（如果适合）
        byte[] content = response.getContentAsByteArray();
        if (content.length > 0 && content.length < 2048 && isJsonResponse(contentType)) {
            String responseBody = new String(content, StandardCharsets.UTF_8);
            logMessage.append(" | 响应体: ").append(responseBody);
        }
        
        logger.debug(logMessage.toString());
    }
    
    /**
     * 是否跳过日志记录
     */
    private boolean shouldSkipLogging(String requestURI) {
        return EXCLUDE_PATHS.stream().anyMatch(requestURI::startsWith);
    }
    
    /**
     * 是否记录请求体
     */
    private boolean shouldLogRequestBody(String contentType) {
        if (contentType == null) {
            return false;
        }
        
        return EXCLUDE_CONTENT_TYPES.stream().noneMatch(contentType::contains) &&
               (contentType.contains("application/json") || contentType.contains("application/x-www-form-urlencoded"));
    }
    
    /**
     * 是否为JSON响应
     */
    private boolean isJsonResponse(String contentType) {
        return contentType != null && contentType.contains("application/json");
    }
}
