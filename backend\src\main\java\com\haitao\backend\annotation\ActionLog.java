package com.haitao.backend.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ActionLog {
    
    /**
     * 操作类型
     */
    String actionType();
    
    /**
     * 操作描述模板，支持SpEL表达式
     * 例如："创建用户：#{#request.username}"
     */
    String description();
    
    /**
     * 目标对象ID的SpEL表达式
     * 例如："#result.data.id" 或 "#userId"
     */
    String targetId() default "";
}
