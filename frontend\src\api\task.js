import request from '../utils/request'

export const taskApi = {
  // 获取任务列表
  getTaskList(params) {
    return request({
      url: '/admin/tasks',
      method: 'get',
      params
    })
  },

  // 获取任务详情
  getTaskDetail(taskId) {
    return request({
      url: `/admin/tasks/${taskId}`,
      method: 'get'
    })
  },

  // 创建任务
  createTask(data) {
    return request({
      url: '/admin/tasks',
      method: 'post',
      data
    })
  },

  // 更新任务
  updateTask(taskId, data) {
    return request({
      url: `/admin/tasks/${taskId}`,
      method: 'put',
      data
    })
  },

  // 删除任务
  deleteTask(taskId) {
    return request({
      url: `/admin/tasks/${taskId}`,
      method: 'delete'
    })
  },

  // 批量删除任务
  batchDeleteTasks(taskIds) {
    return request({
      url: '/admin/tasks/batch',
      method: 'delete',
      data: taskIds
    })
  },

  // 更新任务状态
  updateTaskStatus(taskId, status) {
    return request({
      url: `/admin/tasks/${taskId}/status`,
      method: 'put',
      params: { status }
    })
  },

  // 分配任务
  assignTask(taskId, assignedUserId) {
    return request({
      url: `/admin/tasks/${taskId}/assign`,
      method: 'put',
      params: { assignedUserId }
    })
  }
}
