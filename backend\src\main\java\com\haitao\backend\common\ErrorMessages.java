package com.haitao.backend.common;

/**
 * 错误消息常量类
 */
public class ErrorMessages {
    
    // 登录相关错误消息
    public static final String LOGIN_FAILED = "账号或密码错误";
    public static final String ACCOUNT_DISABLED = "账号已被停用，请联系管理员";
    public static final String TOKEN_INVALID = "认证令牌无效或已过期";
    public static final String TOKEN_MISSING = "未提供认证令牌";
    public static final String TOKEN_PARSE_FAILED = "认证令牌解析失败";
    
    // 权限相关错误消息
    public static final String PERMISSION_DENIED = "权限不足";
    public static final String ADMIN_REQUIRED = "只有管理员才能执行此操作";
    public static final String USER_NOT_LOGGED_IN = "用户未登录";
    
    // 用户管理相关错误消息
    public static final String USER_NOT_FOUND = "用户不存在";
    public static final String USERNAME_REQUIRED = "用户名不能为空";
    public static final String USERNAME_INVALID = "用户名只能包含字母、数字、下划线，长度3-20位";
    public static final String REAL_NAME_REQUIRED = "真实姓名不能为空";
    public static final String ROLE_REQUIRED = "角色不能为空";
    public static final String ROLE_INVALID = "角色值无效";
    public static final String USERNAME_EXISTS = "用户名已存在";
    public static final String PASSWORD_REQUIRED = "密码不能为空";
    public static final String PASSWORD_TOO_SHORT = "密码长度不能少于6位";
    public static final String CANNOT_MODIFY_SELF_ROLE = "不能修改自己的角色";
    public static final String CANNOT_DISABLE_SELF = "不能停用自己的账号";
    public static final String CANNOT_DELETE_SELF = "不能删除自己的账号";
    
    // 业务操作相关错误消息
    public static final String OPERATION_FAILED = "操作失败";
    public static final String DATA_NOT_FOUND = "数据不存在";
    public static final String PARAMETER_ERROR = "参数错误";
    public static final String SYSTEM_ERROR = "系统内部错误，请稍后重试";
    
    // 任务管理相关错误消息
    public static final String TASK_NOT_FOUND = "任务不存在";
    public static final String TASK_ALREADY_ASSIGNED = "任务已被分配";
    public static final String INVALID_TASK_STATUS = "无效的任务状态";
    public static final String TASK_CANNOT_BE_DELETED = "任务无法删除";
    public static final String ASSIGNED_USER_NOT_FOUND = "指定的分配用户不存在";

    // 成功消息
    public static final String LOGIN_SUCCESS = "登录成功";
    public static final String LOGOUT_SUCCESS = "退出登录成功";
    public static final String USER_CREATED = "用户创建成功";
    public static final String USER_UPDATED = "用户信息更新成功";
    public static final String USER_DELETED = "用户删除成功";
    public static final String PASSWORD_RESET = "密码重置成功";
    public static final String STATUS_CHANGED = "用户状态切换成功";
    public static final String TASK_CREATED = "任务创建成功";
    public static final String TASK_UPDATED = "任务更新成功";
    public static final String TASK_DELETED = "任务删除成功";
    public static final String TASK_ASSIGNED = "任务分配成功";
    public static final String TASK_STATUS_UPDATED = "任务状态更新成功";
    public static final String OPERATION_SUCCESS = "操作成功";
}
