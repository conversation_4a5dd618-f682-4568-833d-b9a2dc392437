package com.haitao.backend.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收入统计响应DTO
 */
@Data
public class IncomeStatisticsResponse {
    
    /** 总体统计数据 */
    private OverallStatistics overall;
    
    /** 时间趋势数据 */
    private List<TrendData> trendData;
    
    /** 用户排行数据 */
    private List<UserRankingData> userRanking;
    
    /** 图表数据 */
    private ChartData chartData;
    
    /**
     * 总体统计数据
     */
    @Data
    public static class OverallStatistics {
        /** 总接单金额 */
        private BigDecimal totalAmount;
        
        /** 总实得收入 */
        private BigDecimal totalNetIncome;
        
        /** 总派单员抽成 */
        private BigDecimal totalDispatcherFee;
        
        /** 总抽成金额 */
        private BigDecimal totalCommission;
        
        /** 总任务数量 */
        private Long totalTaskCount;
        
        /** 已完成任务数量 */
        private Long completedTaskCount;
        
        /** 已结算任务数量 */
        private Long settledTaskCount;
        
        /** 平均任务金额 */
        private BigDecimal averageTaskAmount;
        
        /** 平均抽成比例 */
        private BigDecimal averageCommissionRate;
    }
    
    /**
     * 趋势数据
     */
    @Data
    public static class TrendData {
        /** 时间标签（如：2025-01、2025-W03、2025-01-15） */
        private String timeLabel;
        
        /** 该时间段的接单总金额 */
        private BigDecimal totalAmount;
        
        /** 该时间段的实得收入 */
        private BigDecimal netIncome;
        
        /** 该时间段的派单员抽成 */
        private BigDecimal dispatcherFee;
        
        /** 该时间段的任务数量 */
        private Long taskCount;
        
        /** 该时间段的完成任务数量 */
        private Long completedCount;
    }
    
    /**
     * 用户排行数据
     */
    @Data
    public static class UserRankingData {
        /** 用户ID */
        private Long userId;
        
        /** 用户真实姓名 */
        private String realName;
        
        /** 用户名 */
        private String username;
        
        /** 接单总金额 */
        private BigDecimal totalAmount;
        
        /** 实得收入 */
        private BigDecimal netIncome;
        
        /** 任务数量 */
        private Long taskCount;
        
        /** 完成任务数量 */
        private Long completedCount;
        
        /** 平均任务金额 */
        private BigDecimal averageAmount;
        
        /** 排名 */
        private Integer ranking;
    }
    
    /**
     * 图表数据
     */
    @Data
    public static class ChartData {
        /** X轴标签 */
        private List<String> xAxisLabels;
        
        /** 接单总金额数据系列 */
        private List<BigDecimal> totalAmountSeries;
        
        /** 实得收入数据系列 */
        private List<BigDecimal> netIncomeSeries;
        
        /** 派单员抽成数据系列 */
        private List<BigDecimal> dispatcherFeeSeries;
        
        /** 任务数量数据系列 */
        private List<Long> taskCountSeries;
    }
}
