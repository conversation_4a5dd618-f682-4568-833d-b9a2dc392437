package com.haitao.backend.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.haitao.backend.annotation.RequireAuth;
import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.common.ErrorMessages;
import com.haitao.backend.common.Result;
import com.haitao.backend.entity.User;
import com.haitao.backend.mapper.UserMapper;
import com.haitao.backend.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * JWT认证拦截器
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtInterceptor.class);
    
    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserMapper userMapper;
    
    // 不需要认证的路径
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
        "/auth/login",
        "/health",
        "/error",
        "/favicon.ico"
    );
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();

        logger.info("请求拦截: {} {}", method, requestURI);

        // OPTIONS请求直接放行
        if ("OPTIONS".equals(method)) {
            return true;
        }

        // 检查是否为排除路径
        if (isExcludePath(requestURI)) {
            logger.info("排除路径，直接放行: {}", requestURI);
            return true;
        }

        // 检查是否为HandlerMethod
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;

        // 检查方法或类上是否有RequireAuth或RequireAdmin注解
        RequireAuth requireAuth = handlerMethod.getMethodAnnotation(RequireAuth.class);
        RequireAdmin requireAdmin = handlerMethod.getMethodAnnotation(RequireAdmin.class);

        if (requireAuth == null) {
            requireAuth = handlerMethod.getBeanType().getAnnotation(RequireAuth.class);
        }
        if (requireAdmin == null) {
            requireAdmin = handlerMethod.getBeanType().getAnnotation(RequireAdmin.class);
        }

        // 如果没有任何认证注解，直接放行
        if (requireAuth == null && requireAdmin == null) {
            return true;
        }
        
        // 获取token
        String token = getTokenFromRequest(request);

        if (!StringUtils.hasText(token)) {
            String errorMsg = requireAuth != null ? requireAuth.value() :
                             (requireAdmin != null ? requireAdmin.value() : ErrorMessages.TOKEN_MISSING);
            logger.warn("请求缺少token: {}", requestURI);
            writeErrorResponse(response, errorMsg);
            return false;
        }

        // 验证token
        if (!jwtUtil.validateToken(token)) {
            logger.warn("无效的token: {}", token);
            writeErrorResponse(response, ErrorMessages.TOKEN_INVALID);
            return false;
        }

        // 获取用户ID并设置到请求属性中
        Long userId;
        try {
            userId = jwtUtil.getUserIdFromToken(token);
            request.setAttribute("userId", userId);
            logger.info("用户认证成功: userId={}", userId);
        } catch (Exception e) {
            logger.error("解析token失败: {}", e.getMessage());
            writeErrorResponse(response, ErrorMessages.TOKEN_PARSE_FAILED);
            return false;
        }

        // 如果需要管理员权限，检查用户角色
        if (requireAdmin != null) {
            User user = userMapper.selectById(userId);
            if (user == null || user.getRole() != 0) {
                logger.warn("用户无管理员权限: userId={}, role={}", userId, user != null ? user.getRole() : "null");
                writeErrorResponse(response, requireAdmin.value());
                return false;
            }
        }

        return true;
    }
    
    /**
     * 检查是否为排除路径
     */
    private boolean isExcludePath(String requestURI) {
        return EXCLUDE_PATHS.stream().anyMatch(requestURI::startsWith);
    }
    
    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 从Header中获取
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        // 从参数中获取（备用方案）
        return request.getParameter("token");
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        Result<Object> result = Result.unauthorized(message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
    }
}
