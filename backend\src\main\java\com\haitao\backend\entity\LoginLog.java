package com.haitao.backend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 登录日志实体类
 */
@TableName("login_log")
public class LoginLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 登录用户ID
     */
    private Long userId;
    
    /**
     * 登录IP地址
     */
    private String ipAddress;
    
    /**
     * 客户端UA信息
     */
    private String userAgent;
    
    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;
    
    /**
     * 登录结果：1=成功，0=失败
     */
    private Integer loginResult;
    
    // 构造函数
    public LoginLog() {}
    
    public LoginLog(Long userId, String ipAddress, String userAgent, Integer loginResult) {
        this.userId = userId;
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
        this.loginResult = loginResult;
        this.loginTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public LocalDateTime getLoginTime() {
        return loginTime;
    }
    
    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime;
    }
    
    public Integer getLoginResult() {
        return loginResult;
    }
    
    public void setLoginResult(Integer loginResult) {
        this.loginResult = loginResult;
    }
}
