/*
 Navicat Premium Dump SQL

 Source Server         : task_order_db
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : **************:3306
 Source Schema         : task_order_db

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 27/07/2025 19:10:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for action_log
-- ----------------------------
DROP TABLE IF EXISTS `action_log`;
CREATE TABLE `action_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `user_id` bigint NOT NULL COMMENT '执行操作的用户ID',
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型（如CREATE_ORDER、UPDATE_ORDER等）',
  `target_id` bigint NULL DEFAULT NULL COMMENT '被操作对象的ID（如订单ID或用户ID）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作说明，如“修改订单金额为3000元”',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作来源IP',
  `action_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `action_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作行为日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of action_log
-- ----------------------------

-- ----------------------------
-- Table structure for login_log
-- ----------------------------
DROP TABLE IF EXISTS `login_log`;
CREATE TABLE `login_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `user_id` bigint NOT NULL COMMENT '登录用户ID',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录IP地址',
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户端UA信息',
  `login_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `login_result` tinyint NULL DEFAULT 1 COMMENT '登录结果：1=成功，0=失败',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `login_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户登录日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of login_log
-- ----------------------------

-- ----------------------------
-- Table structure for order_task
-- ----------------------------
DROP TABLE IF EXISTS `order_task`;
CREATE TABLE `order_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `project_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `total_price` decimal(10, 2) NOT NULL COMMENT '项目总价（单位：元）',
  `commission_rate` decimal(4, 2) NOT NULL COMMENT '抽成比例（如0.30 表示30%）',
  `net_income` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '实得收入',
  `commission_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '抽成金额',
  `dispatcher_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '派单员抽成金额',
  `assigned_user_id` bigint NULL DEFAULT NULL COMMENT '被分配成员ID',
  `created_by` bigint NOT NULL COMMENT '任务创建人ID（通常为管理员或派单人）',
  `order_time` datetime NOT NULL COMMENT '接单时间',
  `deadline` datetime NULL DEFAULT NULL COMMENT '客户要求完成时间',
  `status` tinyint NULL DEFAULT 0 COMMENT '任务状态：0=待处理，1=进行中，2=已完成，3=客户已取消，4=待结算，5=已结算',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注信息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标志：0=未删除，1=已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `assigned_user_id`(`assigned_user_id` ASC) USING BTREE,
  INDEX `created_by`(`created_by` ASC) USING BTREE,
  CONSTRAINT `order_task_ibfk_1` FOREIGN KEY (`assigned_user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_task_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order_task
-- ----------------------------
INSERT INTO `order_task` VALUES (1, 'test1', '20251313', 100.00, 0.10, 90.00, 10.00, 10.00, 3, 1, '2025-07-27 16:21:23', '2025-07-31 00:00:00', 1, '加急', '2025-07-27 16:21:31', '2025-07-27 17:32:55', 0);

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名（唯一）',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加密后的登录密码',
  `role` tinyint NULL DEFAULT 1 COMMENT '用户角色：0=管理员，1=普通成员',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `status` tinyint NULL DEFAULT 1 COMMENT '账号状态：1=正常，0=停用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '25d55ad283aa400af464c76d713c07ad', 0, '雷海涛', '19892030023', '<EMAIL>', 1, '2025-07-26 21:52:30', '2025-07-27 14:47:45');
INSERT INTO `user` VALUES (3, 'test1', 'e10adc3949ba59abbe56e057f20f883e', 1, '测试1', '19892030021', '<EMAIL>', 1, '2025-07-27 14:18:44', '2025-07-27 16:22:46');

SET FOREIGN_KEY_CHECKS = 1;
