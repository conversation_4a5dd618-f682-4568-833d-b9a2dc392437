<template>
  <div class="task-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="goBack" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>任务详情</h2>
    </div>

    <!-- 任务详情卡片 -->
    <div v-if="taskDetail" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(taskDetail.status)">
              {{ taskDetail.statusName }}
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>订单编号：</label>
              <span>{{ taskDetail.orderNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>项目名称：</label>
              <span>{{ taskDetail.projectName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>创建人：</label>
              <span>{{ taskDetail.createdByName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>分配成员：</label>
              <span>{{ taskDetail.assignedUserName || '未分配' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>接单时间：</label>
              <span>{{ taskDetail.orderTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>截止时间：</label>
              <span>{{ taskDetail.deadline || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ taskDetail.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ taskDetail.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 财务信息 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>财务信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="finance-item">
              <div class="finance-label">项目总价</div>
              <div class="finance-value total-price">¥{{ taskDetail.totalPrice?.toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="finance-item">
              <div class="finance-label">抽成比例</div>
              <div class="finance-value">{{ (taskDetail.commissionRate * 100).toFixed(1) }}%</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="finance-item">
              <div class="finance-label">抽成金额</div>
              <div class="finance-value commission">¥{{ taskDetail.commissionAmount?.toFixed(2) }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="finance-item">
              <div class="finance-label">实得收入</div>
              <div class="finance-value net-income">¥{{ taskDetail.netIncome?.toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="finance-item">
              <div class="finance-label">派单员抽成</div>
              <div class="finance-value">¥{{ taskDetail.dispatcherFee?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="taskDetail.remarks" class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>备注信息</span>
          </div>
        </template>
        
        <div class="remarks-content">
          {{ taskDetail.remarks }}
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑任务
        </el-button>
        <el-button type="success" @click="handleStatusChange">
          <el-icon><Refresh /></el-icon>
          更新状态
        </el-button>
        <el-button type="warning" @click="handleAssign">
          <el-icon><User /></el-icon>
          重新分配
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-empty description="任务不存在或已被删除" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, Refresh, User } from '@element-plus/icons-vue'
import { taskApi } from '../../api/task'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const taskDetail = ref(null)

// 获取状态标签类型
const getStatusType = (status) => {
  const statusTypes = {
    0: '',
    1: 'warning',
    2: 'success',
    3: 'danger',
    4: 'info',
    5: 'success'
  }
  return statusTypes[status] || ''
}

// 获取任务详情
const getTaskDetail = async () => {
  try {
    loading.value = true
    const taskId = route.params.id
    const response = await taskApi.getTaskDetail(taskId)
    if (response.code === 200) {
      taskDetail.value = response.data
    } else {
      ElMessage.error('获取任务详情失败')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.push('/tasks')
}

// 编辑任务
const handleEdit = () => {
  ElMessage.info('编辑功能开发中...')
}

// 更新状态
const handleStatusChange = () => {
  ElMessage.info('状态更新功能开发中...')
}

// 重新分配
const handleAssign = () => {
  ElMessage.info('重新分配功能开发中...')
}

// 页面加载时获取数据
onMounted(() => {
  getTaskDetail()
})
</script>

<style scoped>
.task-detail {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.detail-content {
  max-width: 1200px;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #1f2937;
}

.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #6b7280;
  min-width: 100px;
}

.info-item span {
  color: #1f2937;
}

.finance-item {
  text-align: center;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  margin-bottom: 16px;
}

.finance-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.finance-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.finance-value.total-price {
  color: #3b82f6;
}

.finance-value.commission {
  color: #ef4444;
}

.finance-value.net-income {
  color: #10b981;
}

.remarks-content {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  color: #374151;
  line-height: 1.6;
}

.action-section {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.loading-container,
.error-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-detail {
    padding: 12px;
  }
  
  .finance-item {
    margin-bottom: 12px;
  }
  
  .action-section {
    flex-direction: column;
  }
}
</style>
