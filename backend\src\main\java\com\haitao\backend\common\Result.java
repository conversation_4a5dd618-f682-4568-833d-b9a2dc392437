package com.haitao.backend.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 统一API响应结果类
 * 只包含三个字段：code、message、data
 */
@Data
public class Result<T> {
    /** 响应状态码 */
    private Integer code;
    /** 响应消息 */
    private String message;
    /** 响应数据 */
    private T data;

    public Result() {}

    public Result(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    // 成功返回（带数据）
    public static <T> Result<T> success(T data) {
        return new Result<>(ResponseCode.SUCCESS, ResponseCode.getDescription(ResponseCode.SUCCESS), data);
    }

    // 成功返回（无数据）
    public static Result<Void> success() {
        return new Result<>(ResponseCode.SUCCESS, ResponseCode.getDescription(ResponseCode.SUCCESS), null);
    }

    // 成功返回（自定义消息，无数据）
    public static Result<Void> success(String message) {
        return new Result<>(ResponseCode.SUCCESS, message, null);
    }

    // 成功返回（自定义消息，带数据）
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResponseCode.SUCCESS, message, data);
    }
    
    // 失败返回
    public static <T> Result<T> error(String message) {
        return new Result<>(ResponseCode.INTERNAL_SERVER_ERROR, message, null);
    }

    // 失败返回（自定义状态码）
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null);
    }

    // 参数错误
    public static <T> Result<T> badRequest(String message) {
        return new Result<>(ResponseCode.BAD_REQUEST, message, null);
    }

    // 未授权
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(ResponseCode.UNAUTHORIZED, message, null);
    }

    // 禁止访问
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(ResponseCode.FORBIDDEN, message, null);
    }

    // 判断是否成功（不参与JSON序列化）
    @JsonIgnore
    public boolean isSuccess() {
        return this.code != null && this.code == ResponseCode.SUCCESS;
    }

    // 判断是否失败（不参与JSON序列化）
    @JsonIgnore
    public boolean isError() {
        return !isSuccess();
    }
}