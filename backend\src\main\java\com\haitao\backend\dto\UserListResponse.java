package com.haitao.backend.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户列表响应DTO
 */
@Data
public class UserListResponse {

    private Long id;

    private String username;

    private String realName;

    /** 角色：0=管理员，1=普通成员 */
    private Integer role;

    /** 角色名称 */
    private String roleName;

    private String phone;

    private String email;

    /** 状态：1=正常，0=停用 */
    private Integer status;

    /** 状态名称 */
    private String statusName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
