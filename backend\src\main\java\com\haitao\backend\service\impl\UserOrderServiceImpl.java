package com.haitao.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.entity.User;
import com.haitao.backend.dto.CreateTaskRequest;
import com.haitao.backend.dto.UpdateTaskRequest;
import com.haitao.backend.dto.TaskQueryRequest;
import com.haitao.backend.mapper.OrderTaskMapper;
import com.haitao.backend.mapper.UserMapper;
import com.haitao.backend.service.UserOrderService;
import com.haitao.backend.exception.BusinessException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * 用户订单服务实现
 */
@Service
public class UserOrderServiceImpl implements UserOrderService {
    
    @Autowired
    private OrderTaskMapper orderTaskMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public IPage<OrderTask> getMyOrderList(TaskQueryRequest request, Long userId) {
        Page<OrderTask> page = new Page<>(request.getPage(), request.getSize());
        
        LambdaQueryWrapper<OrderTask> wrapper = new LambdaQueryWrapper<>();
        
        // 只查询当前用户的订单
        wrapper.eq(OrderTask::getAssignedUserId, userId);
        
        // 关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            wrapper.and(w -> w.like(OrderTask::getProjectName, request.getKeyword())
                           .or().like(OrderTask::getOrderNumber, request.getKeyword()));
        }
        
        // 状态筛选
        if (request.getStatus() != null) {
            wrapper.eq(OrderTask::getStatus, request.getStatus());
        }
        
        // 按创建时间倒序
        wrapper.orderByDesc(OrderTask::getOrderTime);
        
        return orderTaskMapper.selectPage(page, wrapper);
    }
    
    @Override
    public OrderTask getMyOrderDetail(Long orderId, Long userId) {
        OrderTask order = orderTaskMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 验证订单所有权
        if (!userId.equals(order.getAssignedUserId())) {
            throw new BusinessException("无权访问此订单");
        }
        
        return order;
    }
    
    @Override
    @Transactional
    public OrderTask createOrder(CreateTaskRequest request, Long userId) {
        // 验证用户存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证请求参数
        validateCreateOrderRequest(request);

        OrderTask order = new OrderTask();
        BeanUtils.copyProperties(request, order);

        // 处理订单编号
        String orderNumber = request.getOrderNumber();
        if (!StringUtils.hasText(orderNumber)) {
            orderNumber = generateOrderNumber();
        }
        order.setOrderNumber(orderNumber);

        // 设置订单基本信息
        order.setAssignedUserId(userId);
        order.setStatus(0); // 待处理

        // 处理接单时间
        if (order.getOrderTime() == null) {
            order.setOrderTime(LocalDateTime.now());
        }

        order.setCreatedBy(userId);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        // 验证抽成比例
        if (order.getCommissionRate() == null) {
            throw new BusinessException("抽成比例不能为空");
        }

        // 计算财务数据
        calculateOrderFinance(order);

        orderTaskMapper.insert(order);
        return order;
    }
    
    @Override
    @Transactional
    public OrderTask updateOrder(Long orderId, UpdateTaskRequest request, Long userId) {
        OrderTask existingOrder = getMyOrderDetail(orderId, userId);
        
        // 用户可以编辑自己的订单，包括更新状态
        
        // 验证请求参数
        validateUpdateOrderRequest(request);

        // 更新订单信息（保护关键系统字段）
        BeanUtils.copyProperties(request, existingOrder, "id", "orderNumber", "assignedUserId",
                                "orderTime", "createdBy", "createTime");

        existingOrder.setUpdateTime(LocalDateTime.now());

        // 重新计算财务数据
        calculateOrderFinance(existingOrder);
        
        orderTaskMapper.updateById(existingOrder);
        return existingOrder;
    }
    
    @Override
    @Transactional
    public void deleteOrder(Long orderId, Long userId) {
        OrderTask order = getMyOrderDetail(orderId, userId);
        
        // 只有待处理状态的订单才能删除
        if (order.getStatus() != 0) {
            throw new BusinessException("只有待处理状态的订单才能删除");
        }
        
        orderTaskMapper.deleteById(orderId);
    }
    
    @Override
    @Transactional
    public OrderTask cancelOrder(Long orderId, Long userId) {
        OrderTask order = getMyOrderDetail(orderId, userId);
        
        // 只有待处理和进行中状态的订单才能取消
        if (order.getStatus() != 0 && order.getStatus() != 1) {
            throw new BusinessException("只有待处理和进行中状态的订单才能取消");
        }
        
        order.setStatus(3); // 客户已取消
        order.setUpdateTime(LocalDateTime.now());
        
        orderTaskMapper.updateById(order);
        return order;
    }
    
    /**
     * 验证更新订单请求参数
     */
    private void validateUpdateOrderRequest(UpdateTaskRequest request) {
        // 验证项目名称
        if (StringUtils.hasText(request.getProjectName())) {
            if (request.getProjectName().length() > 100) {
                throw new BusinessException("项目名称长度不能超过100个字符");
            }
        }

        // 验证项目总价
        if (request.getTotalPrice() != null) {
            if (request.getTotalPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BusinessException("项目总价必须大于0");
            }
            if (request.getTotalPrice().compareTo(new BigDecimal("99999999.99")) > 0) {
                throw new BusinessException("项目总价不能超过99999999.99元");
            }
        }

        // 验证抽成比例
        if (request.getCommissionRate() != null) {
            if (request.getCommissionRate().compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException("抽成比例不能小于0");
            }
            if (request.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
                throw new BusinessException("抽成比例不能大于1");
            }
        }

        // 验证截止时间
        if (request.getDeadline() != null && request.getDeadline().isBefore(LocalDateTime.now())) {
            throw new BusinessException("截止时间不能早于当前时间");
        }
    }

    /**
     * 验证创建订单请求参数
     */
    private void validateCreateOrderRequest(CreateTaskRequest request) {
        // 验证项目名称
        if (!StringUtils.hasText(request.getProjectName())) {
            throw new BusinessException("项目名称不能为空");
        }
        if (request.getProjectName().length() > 100) {
            throw new BusinessException("项目名称长度不能超过100个字符");
        }

        // 验证项目总价
        if (request.getTotalPrice() == null) {
            throw new BusinessException("项目总价不能为空");
        }
        if (request.getTotalPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("项目总价必须大于0");
        }
        if (request.getTotalPrice().compareTo(new BigDecimal("99999999.99")) > 0) {
            throw new BusinessException("项目总价不能超过99999999.99元");
        }

        // 验证抽成比例
        if (request.getCommissionRate() == null) {
            throw new BusinessException("抽成比例不能为空");
        }
        if (request.getCommissionRate().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("抽成比例不能小于0");
        }
        if (request.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
            throw new BusinessException("抽成比例不能大于1");
        }

        // 验证截止时间
        if (request.getDeadline() != null && request.getDeadline().isBefore(LocalDateTime.now())) {
            throw new BusinessException("截止时间不能早于当前时间");
        }
    }

    /**
     * 计算订单财务数据
     */
    private void calculateOrderFinance(OrderTask order) {
        if (order.getTotalPrice() == null || order.getCommissionRate() == null) {
            return;
        }

        // 计算抽成金额 = 总价 * 抽成比例
        BigDecimal commissionAmount = order.getTotalPrice()
            .multiply(order.getCommissionRate())
            .setScale(2, BigDecimal.ROUND_HALF_UP);
        order.setCommissionAmount(commissionAmount);

        // 派单员抽成等于抽成金额（保存两份相同数据）
        order.setDispatcherFee(commissionAmount);

        // 计算实得收入 = 总价 - 抽成金额（只减去一次，因为抽成金额就是派单员抽成）
        BigDecimal netIncome = order.getTotalPrice()
            .subtract(commissionAmount)
            .setScale(2, BigDecimal.ROUND_HALF_UP);
        order.setNetIncome(netIncome);
    }

    /**
     * 生成订单编号
     */
    private String generateOrderNumber() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "ORD" + timestamp + uuid.toUpperCase();
    }
}
