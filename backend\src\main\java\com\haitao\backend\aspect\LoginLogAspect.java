package com.haitao.backend.aspect;

import com.haitao.backend.annotation.LoginLog;
import com.haitao.backend.common.Result;
import com.haitao.backend.dto.LoginRequest;
import com.haitao.backend.dto.LoginResponse;
import com.haitao.backend.entity.User;
import com.haitao.backend.mapper.UserMapper;
import com.haitao.backend.service.LoginLogService;
import com.haitao.backend.util.IpUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录日志切面
 */
@Aspect
@Component
public class LoginLogAspect {

    @Autowired
    private LoginLogService loginLogService;

    @Autowired
    private UserMapper userMapper;

    /**
     * 登录成功后记录日志
     */
    @AfterReturning(pointcut = "@annotation(loginLog)", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, LoginLog loginLog, Object result) {
        try {
            // 获取当前请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取IP地址和User-Agent
            String ipAddress = IpUtil.getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            if (userAgent != null && userAgent.length() > 255) {
                userAgent = userAgent.substring(0, 255); // 限制长度
            }

            // 判断登录结果
            Integer loginResult = 0; // 默认失败
            Long userId = null;

            if (result instanceof Result) {
                Result<?> apiResult = (Result<?>) result;
                if (apiResult.getCode() == 200 && apiResult.getData() instanceof LoginResponse) {
                    LoginResponse loginResponse = (LoginResponse) apiResult.getData();
                    userId = loginResponse.getUserId();
                    loginResult = 1; // 成功
                }
            }

            // 如果登录失败，尝试从请求参数中获取用户信息
            if (userId == null) {
                userId = getUserIdFromRequest(joinPoint);
            }

            // 保存登录日志
            if (userId != null) {
                loginLogService.saveLoginLog(userId, ipAddress, userAgent, loginResult);
            }

        } catch (Exception e) {
            // 日志记录失败不应该影响业务流程
            e.printStackTrace();
        }
    }

    /**
     * 登录异常时记录日志
     */
    @AfterThrowing(pointcut = "@annotation(loginLog)", throwing = "ex")
    public void doAfterThrowing(JoinPoint joinPoint, LoginLog loginLog, Throwable ex) {
        try {
            // 获取当前请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取IP地址和User-Agent
            String ipAddress = IpUtil.getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            if (userAgent != null && userAgent.length() > 255) {
                userAgent = userAgent.substring(0, 255); // 限制长度
            }

            // 尝试从请求参数中获取用户信息
            Long userId = getUserIdFromRequest(joinPoint);

            // 记录登录失败日志
            if (userId != null) {
                loginLogService.saveLoginLog(userId, ipAddress, userAgent, 0); // 失败
            }

        } catch (Exception e) {
            // 日志记录失败不应该影响业务流程
            e.printStackTrace();
        }
    }

    /**
     * 从请求参数中获取用户ID
     */
    private Long getUserIdFromRequest(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            for (Object arg : args) {
                if (arg instanceof LoginRequest) {
                    LoginRequest loginRequest = (LoginRequest) arg;
                    String username = loginRequest.getUsername();
                    if (username != null && !username.trim().isEmpty()) {
                        // 使用MyBatis-Plus的Lambda条件构造器查询用户
                        User user = userMapper.selectOne(
                            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<User>()
                                .eq(User::getUsername, username)
                                .last("LIMIT 1")
                        );
                        if (user != null) {
                            return user.getId();
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }
}
