package com.haitao.backend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单任务实体类
 */
@Data
@TableName("order_task")
public class OrderTask {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 项目名称 */
    private String projectName;
    
    /** 订单编号 */
    private String orderNumber;
    
    /** 项目总价 */
    private BigDecimal totalPrice;
    
    /** 抽成比例 */
    private BigDecimal commissionRate;
    
    /** 实得收入（自动计算） */
    private BigDecimal netIncome;

    /** 抽成金额（自动计算） */
    private BigDecimal commissionAmount;
    
    /** 派单员抽成金额 */
    private BigDecimal dispatcherFee;
    
    /** 被分配成员ID */
    private Long assignedUserId;
    
    /** 任务创建人ID */
    private Long createdBy;
    
    /** 接单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    
    /** 客户要求完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;
    
    /** 任务状态：0=待处理，1=进行中，2=已完成，3=客户已取消，4=待结算，5=已结算 */
    private Integer status;
    
    /** 备注信息 */
    private String remarks;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /** 逻辑删除标志 */
    @TableLogic
    private Integer isDeleted;
}
