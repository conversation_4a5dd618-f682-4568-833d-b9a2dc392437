package com.haitao.backend.controller;

import com.haitao.backend.annotation.ActionLog;
import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.annotation.RequireAuth;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.Result;
import com.haitao.backend.dto.StatisticsQueryRequest;
import com.haitao.backend.dto.IncomeStatisticsResponse;
import com.haitao.backend.dto.UserIncomeDetailResponse;
import com.haitao.backend.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 统计分析控制器
 */
@RestController
@RequestMapping("/admin/statistics")
public class StatisticsController {
    
    @Autowired
    private StatisticsService statisticsService;
    
    /**
     * 获取收入统计数据
     */
    @PostMapping("/income")
    @RequireAdmin("查看收入统计需要管理员权限")
    @ActionLog(actionType = "VIEW_INCOME_STATISTICS", description = "查看收入统计数据")
    public Result<IncomeStatisticsResponse> getIncomeStatistics(@RequestBody StatisticsQueryRequest query,
                                                               HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        IncomeStatisticsResponse statistics = statisticsService.getIncomeStatistics(query, operatorId);
        return ApiResponse.success("获取收入统计成功", statistics);
    }
    
    /**
     * 获取用户收入详情
     */
    @PostMapping("/user/{userId}/income")
    @RequireAdmin("查看用户收入详情需要管理员权限")
    @ActionLog(actionType = "VIEW_USER_INCOME_DETAIL", description = "查看用户收入详情", targetId = "#arg0")
    public Result<UserIncomeDetailResponse> getUserIncomeDetail(@PathVariable Long userId,
                                                               @RequestBody StatisticsQueryRequest query,
                                                               HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        UserIncomeDetailResponse detail = statisticsService.getUserIncomeDetail(userId, query, operatorId);
        return ApiResponse.success("获取用户收入详情成功", detail);
    }
    
    /**
     * 获取收入趋势图表数据
     */
    @PostMapping("/income/chart")
    @RequireAdmin("查看收入趋势图表需要管理员权限")
    @ActionLog(actionType = "VIEW_INCOME_CHART", description = "查看收入趋势图表")
    public Result<IncomeStatisticsResponse.ChartData> getIncomeChartData(@RequestBody StatisticsQueryRequest query,
                                                                        HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        IncomeStatisticsResponse.ChartData chartData = statisticsService.getIncomeChartData(query, operatorId);
        return ApiResponse.success("获取图表数据成功", chartData);
    }
    
    /**
     * 获取用户排行榜
     */
    @PostMapping("/ranking/users")
    @RequireAdmin("查看用户排行榜需要管理员权限")
    @ActionLog(actionType = "VIEW_USER_RANKING", description = "查看用户排行榜")
    public Result<IncomeStatisticsResponse> getUserRanking(@RequestBody StatisticsQueryRequest query,
                                                          HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        IncomeStatisticsResponse ranking = statisticsService.getUserRanking(query, operatorId);
        return ApiResponse.success("获取用户排行榜成功", ranking);
    }
    
    /**
     * 获取派单员抽成排行榜
     */
    @PostMapping("/ranking/dispatchers")
    @RequireAdmin("查看派单员排行榜需要管理员权限")
    @ActionLog(actionType = "VIEW_DISPATCHER_RANKING", description = "查看派单员抽成排行榜")
    public Result<IncomeStatisticsResponse> getDispatcherRanking(@RequestBody StatisticsQueryRequest query,
                                                                HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        IncomeStatisticsResponse ranking = statisticsService.getDispatcherRanking(query, operatorId);
        return ApiResponse.success("获取派单员排行榜成功", ranking);
    }
    
    /**
     * 获取当前用户的收入统计（普通用户可访问）
     */
    @PostMapping("/my-income")
    @RequireAuth("查看个人收入统计需要登录")
    @ActionLog(actionType = "VIEW_MY_INCOME", description = "查看个人收入统计")
    public Result<IncomeStatisticsResponse> getMyIncomeStatistics(@RequestBody StatisticsQueryRequest query,
                                                                 HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        // 设置查询条件为当前用户
        query.setUserId(operatorId);
        IncomeStatisticsResponse statistics = statisticsService.getIncomeStatistics(query, operatorId);
        return ApiResponse.success("获取个人收入统计成功", statistics);
    }
    
    /**
     * 获取当前用户的收入详情（普通用户可访问）
     */
    @PostMapping("/my-income/detail")
    @RequireAuth("查看个人收入详情需要登录")
    @ActionLog(actionType = "VIEW_MY_INCOME_DETAIL", description = "查看个人收入详情")
    public Result<UserIncomeDetailResponse> getMyIncomeDetail(@RequestBody StatisticsQueryRequest query,
                                                             HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        UserIncomeDetailResponse detail = statisticsService.getUserIncomeDetail(operatorId, query, operatorId);
        return ApiResponse.success("获取个人收入详情成功", detail);
    }
    
    /**
     * 快速获取统计概览（仅返回关键指标）
     */
    @GetMapping("/overview")
    @RequireAdmin("查看统计概览需要管理员权限")
    @ActionLog(actionType = "VIEW_STATISTICS_OVERVIEW", description = "查看统计概览")
    public Result<IncomeStatisticsResponse.OverallStatistics> getStatisticsOverview(HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        
        // 创建默认查询条件（最近30天）
        StatisticsQueryRequest query = new StatisticsQueryRequest();
        query.setTimeDimension("day");
        
        IncomeStatisticsResponse statistics = statisticsService.getIncomeStatistics(query, operatorId);
        return ApiResponse.success("获取统计概览成功", statistics.getOverall());
    }
}
