<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="top-header">
      <!-- 左侧品牌区域 -->
      <div class="header-left">
        <div class="brand-section">
          <img src="/haipao.svg" alt="海泡订单系统" class="brand-logo" />
          <span class="system-title">海泡订单系统</span>
        </div>

        <!-- 主导航菜单 -->
        <el-menu
          ref="menuRef"
          :default-active="activeMenu"
          mode="horizontal"
          router
          class="main-menu"
        >
          <el-menu-item
            v-for="route in visibleMenuRoutes"
            :key="route.path"
            :index="route.path"
            @click="handleMenuClick(route)"
          >
            <el-icon><component :is="route.meta.icon" /></el-icon>
            <span>{{ route.meta.title }}</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧用户区域 -->
      <div class="header-right">
        <!-- 面包屑导航 -->
        <el-breadcrumb separator="/" class="breadcrumb" v-if="currentRoute.meta.title !== '首页'">
          <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentRoute.meta.title }}</el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 用户下拉菜单 -->
        <el-dropdown @command="handleCommand" class="user-dropdown-container" trigger="click">
          <div class="user-dropdown">
            <div class="user-avatar-wrapper">
              <el-avatar :size="36" :src="userAvatar" class="user-avatar">
                {{ userStore.userInfo.realName?.charAt(0) || 'U' }}
              </el-avatar>
              <div class="online-indicator"></div>
            </div>
            <div class="user-info">
              <span class="username">{{ userStore.userInfo.realName || userStore.userInfo.username }}</span>
              <span class="user-role">{{ userStore.userInfo.role === 0 ? '管理员' : '普通用户' }}</span>
            </div>
            <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="custom-dropdown-menu">
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <router-view />
    </el-main>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { ArrowDown, User, Setting, SwitchButton } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const userAvatar = ref('')
const menuRef = ref()

// 当前路由
const currentRoute = computed(() => route)

// 当前激活的菜单 - 优化性能版本
const activeMenu = computed(() => {
  const currentPath = route.path

  // 使用缓存的路径映射提高性能
  const pathMap = {
    '/': '/dashboard',
    '/dashboard': '/dashboard',
    '/users': '/users',
    '/tasks': '/tasks',
    '/logs': '/logs',
    '/statistics': '/statistics',
    '/my-statistics': '/my-statistics',
    '/my-orders': '/my-orders'
  }

  // 直接映射匹配
  if (pathMap[currentPath]) {
    return pathMap[currentPath]
  }

  // 子路径匹配
  if (currentPath.startsWith('/users')) return '/users'
  if (currentPath.startsWith('/tasks')) return '/tasks'
  if (currentPath.startsWith('/logs')) return '/logs'
  if (currentPath.startsWith('/statistics')) return '/statistics'
  if (currentPath.startsWith('/my-statistics')) return '/my-statistics'
  if (currentPath.startsWith('/my-orders')) return '/my-orders'

  return currentPath
})

// 菜单路由（从路由配置中获取）
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  const layoutRoute = routes.find(r => r.path === '/')
  const children = layoutRoute?.children || []

  // 将相对路径转换为绝对路径
  return children.map(child => ({
    ...child,
    path: child.path.startsWith('/') ? child.path : `/${child.path}`,
    fullPath: child.path.startsWith('/') ? child.path : `/${child.path}`
  }))
})

// 可见的菜单路由（过滤权限）
const visibleMenuRoutes = computed(() => {
  return menuRoutes.value.filter(route => {
    if (route.hidden) return false
    return hasPermission(route)
  })
})

// 权限检查
const hasPermission = (route) => {
  if (!route.meta) return true

  // 检查是否需要管理员权限
  if (route.meta.requiresAdmin) {
    return userStore.userInfo.role === 0
  }

  // 检查是否只有普通用户可见
  if (route.meta.userOnly) {
    return userStore.userInfo.role !== 0
  }

  return true
}

// 菜单点击处理 - 性能优化版本
const handleMenuClick = (menuRoute) => {
  // 如果已经在当前路由，不需要跳转
  if (route.path === menuRoute.path) {
    return
  }

  // 立即更新菜单状态，提供即时反馈
  if (menuRef.value) {
    menuRef.value.activeIndex = menuRoute.path
  }

  // 异步执行路由跳转，不阻塞UI
  router.push(menuRoute.path).catch(error => {
    console.error('路由跳转失败:', error)
    // 如果跳转失败，恢复原来的菜单状态
    if (menuRef.value) {
      menuRef.value.activeIndex = activeMenu.value
    }
  })
}

// 用户下拉菜单处理
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听路由变化，更新页面标题
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - 海泡订单系统`
    }
  },
  { immediate: true }
)

// 监听路由变化，确保菜单高亮正确 - 性能优化版本
let lastActiveMenu = ''
let updateTimer = null

watch(
  () => activeMenu.value,
  (newActiveMenu) => {
    // 只有当激活菜单真正改变时才更新DOM
    if (newActiveMenu !== lastActiveMenu) {
      lastActiveMenu = newActiveMenu

      // 清除之前的定时器，实现防抖
      if (updateTimer) {
        cancelAnimationFrame(updateTimer)
      }

      // 使用requestAnimationFrame确保流畅的动画
      updateTimer = requestAnimationFrame(() => {
        if (menuRef.value && menuRef.value.activeIndex !== newActiveMenu) {
          menuRef.value.activeIndex = newActiveMenu
        }
        updateTimer = null
      })
    }
  },
  { immediate: true }
)

// 组件挂载后初始化菜单状态 - 性能优化版本
onMounted(() => {
  // 使用requestAnimationFrame确保DOM完全渲染后再设置状态
  requestAnimationFrame(() => {
    if (menuRef.value && activeMenu.value) {
      menuRef.value.activeIndex = activeMenu.value
      lastActiveMenu = activeMenu.value
    }
  })
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.top-header {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(14, 165, 233, 0.95) 25%,
    rgba(6, 182, 212, 0.9) 50%,
    rgba(8, 145, 178, 0.95) 75%,
    rgba(14, 116, 144, 0.9) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 0 20px rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(15px);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 40px;
  flex: 1;
  min-width: 0;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
  min-width: 200px;
}

.brand-logo {
  width: 40px;
  height: 40px;
  filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
  transition: all 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.1) rotate(5deg);
  filter: brightness(0) invert(1) drop-shadow(0 0 12px rgba(255, 255, 255, 0.8));
}

.system-title {
  color: white;
  font-size: 20px;
  font-weight: 700;
  white-space: nowrap;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  letter-spacing: 1px;
}

.main-menu {
  border: none;
  background: transparent;
  flex: 1;
  min-width: 0;
  /* 优化渲染性能 */
  contain: layout style;
  transform: translateZ(0);
}

.main-menu .el-menu-item {
  color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 3px solid transparent;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 10px 10px 0 0;
  margin: 0 6px;
  padding: 0 15px;
  position: relative;
  overflow: hidden;
  font-size: 15px;
  font-weight: 500;
  min-width: 100px;
  justify-content: center;
  /* 启用硬件加速 */
  will-change: transform, background, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 确保图标颜色继承父元素 */
.main-menu .el-menu-item .el-icon {
  color: inherit;
  transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 确保文字颜色继承父元素 */
.main-menu .el-menu-item span {
  color: inherit;
  transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-menu .el-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  /* 硬件加速 */
  will-change: left;
  transform: translateZ(0);
}

.main-menu .el-menu-item:hover::before {
  left: 100%;
}

.main-menu .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.9) !important;
  border-bottom-color: transparent;
  box-shadow:
    0 0 15px rgba(255, 255, 255, 0.2),
    inset 0 0 10px rgba(255, 255, 255, 0.08);
  transform: translateY(-1px) translateZ(0);
  text-shadow: none;
}

/* 确保选中状态的悬停效果不会被覆盖 */
.main-menu .el-menu-item.is-active:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.3) 100%);
  color: rgba(255, 255, 255, 0.8) !important;
  box-shadow:
    0 0 25px rgba(255, 255, 255, 0.35),
    inset 0 0 15px rgba(255, 255, 255, 0.12);
  text-shadow: none;
  transform: translateY(-1px) translateZ(0);
}

/* 强制覆盖Element Plus的默认颜色 */
.main-menu .el-menu-item:hover .el-icon,
.main-menu .el-menu-item:hover span,
.main-menu .el-menu-item.is-active .el-icon,
.main-menu .el-menu-item.is-active span,
.main-menu .el-menu-item.is-active:hover .el-icon,
.main-menu .el-menu-item.is-active:hover span {
  color: inherit !important;
}

.main-menu .el-menu-item.is-active {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.25) 100%);
  color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 3px solid transparent;
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.25),
    inset 0 0 12px rgba(255, 255, 255, 0.08);
  font-weight: 700;
  text-shadow: none;
  transform: translateY(-1px) translateZ(0);
  position: relative;
  z-index: 10;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-shrink: 0;
}

.breadcrumb {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.breadcrumb :deep(.el-breadcrumb__inner) {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s ease;
}

.breadcrumb :deep(.el-breadcrumb__inner:hover) {
  color: white;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.breadcrumb :deep(.el-breadcrumb__separator) {
  color: rgba(255, 255, 255, 0.6);
}

.user-dropdown-container {
  margin-left: 20px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 16px 8px 8px;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  min-width: 180px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-dropdown::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.user-dropdown:hover::before {
  left: 100%;
}

.user-dropdown:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.user-avatar-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.user-avatar {
  background: linear-gradient(135deg, #2dd4bf 0%, #0ea5e9 100%);
  color: white;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.5);
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 10px;
  height: 10px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.username {
  color: white;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-role {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-weight: 400;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.user-dropdown:hover .dropdown-arrow {
  color: white;
  transform: rotate(180deg);
}

/* 自定义下拉菜单样式 */
:deep(.custom-dropdown-menu) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 8px;
  min-width: 240px;
  margin-top: 8px;
}

:deep(.dropdown-header) {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  margin: -8px -8px 8px -8px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(14, 165, 233, 0.1) 100%);
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.header-avatar) {
  background: linear-gradient(135deg, #2dd4bf 0%, #0ea5e9 100%);
  color: white;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

:deep(.header-info) {
  flex: 1;
  min-width: 0;
}

:deep(.header-name) {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  margin-bottom: 2px;
}

:deep(.header-role) {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

:deep(.dropdown-item) {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 2px 0;
  border-radius: 10px;
  transition: all 0.2s ease;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

:deep(.dropdown-item:hover) {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(14, 165, 233, 0.1) 100%);
  color: #1f2937;
  transform: translateX(4px);
}

:deep(.dropdown-item .el-icon) {
  font-size: 16px;
  color: #6b7280;
  transition: color 0.2s ease;
}

:deep(.dropdown-item:hover .el-icon) {
  color: #3b82f6;
}

:deep(.logout-item) {
  margin-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 16px;
}

:deep(.logout-item:hover) {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.1) 0%,
    rgba(220, 38, 38, 0.1) 100%);
  color: #dc2626;
}

:deep(.logout-item:hover .el-icon) {
  color: #dc2626;
}

.main-content {
  flex: 1;
  background: #f0f2f5;
  overflow-y: auto;
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-left {
    gap: 30px;
  }

  .main-menu .el-menu-item {
    min-width: 100px;
    padding: 0 16px;
    font-size: 14px;
  }

  .brand-section {
    min-width: 180px;
  }

  .system-title {
    font-size: 18px;
  }
}

@media (max-width: 992px) {
  .header-left {
    gap: 20px;
  }

  .main-menu .el-menu-item {
    min-width: 80px;
    padding: 0 12px;
    margin: 0 3px;
  }

  .brand-section {
    min-width: 160px;
  }

  .breadcrumb {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-left {
    gap: 15px;
  }

  .system-title {
    display: none;
  }

  .brand-section {
    min-width: auto;
  }

  .main-menu .el-menu-item {
    min-width: 60px;
    padding: 0 8px;
    margin: 0 2px;
  }

  .main-menu .el-menu-item span {
    display: none;
  }

  .header-right {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .top-header {
    padding: 0 12px;
  }

  .header-left {
    gap: 10px;
  }

  .main-menu .el-menu-item {
    min-width: 50px;
    padding: 0 6px;
    margin: 0 1px;
  }

  .user-dropdown {
    padding: 6px 8px 6px 6px;
    min-width: auto;
    gap: 8px;
  }

  .user-info {
    display: none;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  :deep(.custom-dropdown-menu) {
    min-width: 200px;
  }

  :deep(.dropdown-header) {
    padding: 12px;
  }

  :deep(.header-name) {
    font-size: 14px;
  }
}
</style>
