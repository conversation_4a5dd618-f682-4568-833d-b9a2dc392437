package com.haitao.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.entity.ActionLog;
import com.haitao.backend.dto.ActionLogResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 操作日志Mapper
 */
@Mapper
public interface ActionLogMapper extends BaseMapper<ActionLog> {
    
    /**
     * 分页查询操作日志（带用户信息）
     */
    @Select("SELECT al.*, u.username, u.real_name " +
            "FROM action_log al " +
            "LEFT JOIN user u ON al.user_id = u.id " +
            "${ew.customSqlSegment}")
    IPage<ActionLogResponse> selectActionLogWithUserInfo(Page<ActionLogResponse> page, @Param("ew") Object wrapper);
}
