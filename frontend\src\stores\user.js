import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import request from '../utils/request'

export const useUserStore = defineStore('user', () => {
  const token = ref('')
  const userInfo = ref({})
  const isLoggedIn = ref(false)
  const isInitialized = ref(false) // 添加初始化状态标记

  // 登录
  const login = async (loginForm) => {
    try {
      const response = await request.post('/auth/login', loginForm)

      if (response.code === 200) {
        const data = response.data

        // 更新状态
        token.value = data.token
        userInfo.value = {
          userId: data.userId,
          username: data.username,
          realName: data.realName,
          role: data.role
        }
        isLoggedIn.value = true

        // 保存到localStorage
        localStorage.setItem('token', data.token)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

        ElMessage.success(response.message)
        return { success: true }
      } else {
        ElMessage.error(response.message)
        return { success: false, message: response.message }
      }
    } catch (error) {
      const message = error.response?.data?.message || '登录失败'
      ElMessage.error(message)
      return { success: false, message }
    }
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    userInfo.value = {}
    isLoggedIn.value = false

    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    ElMessage.success('退出登录成功')
  }

  // 初始化用户信息（同步版本）
  const initUserInfo = () => {
    if (isInitialized.value) return

    const storedToken = localStorage.getItem('token')
    const storedUserInfo = localStorage.getItem('userInfo')

    if (storedToken && storedUserInfo) {
      try {
        token.value = storedToken
        userInfo.value = JSON.parse(storedUserInfo)
        isLoggedIn.value = true
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }

    isInitialized.value = true
  }

  // 检查token有效性
  const checkTokenValidity = async () => {
    if (!token.value) return false

    try {
      // 这里可以调用后端接口验证token
      // const response = await request.get('/auth/verify')
      // return response.data.code === 200

      // 暂时返回true，如果需要可以添加token验证接口
      return true
    } catch (error) {
      console.error('Token验证失败:', error)
      logout()
      return false
    }
  }

  // 监听状态变化，自动保存到localStorage
  watch([token, userInfo, isLoggedIn], () => {
    if (isLoggedIn.value && token.value) {
      localStorage.setItem('token', token.value)
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    }
  }, { deep: true })

  // 立即初始化（在store创建时）
  initUserInfo()

  return {
    token,
    userInfo,
    isLoggedIn,
    isInitialized,
    login,
    logout,
    initUserInfo,
    checkTokenValidity
  }
})

