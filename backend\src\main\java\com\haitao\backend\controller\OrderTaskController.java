package com.haitao.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.annotation.ActionLog;
import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.Result;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.dto.*;
import com.haitao.backend.service.OrderTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 订单任务管理控制器
 */
@RestController
@RequestMapping("/admin/tasks")
@RequireAdmin("任务管理需要管理员权限")
public class OrderTaskController {
    
    @Autowired
    private OrderTaskService orderTaskService;
    
    /**
     * 创建任务
     */
    @PostMapping
    @ActionLog(actionType = "CREATE_TASK", description = "创建任务：#{#arg0.projectName}", targetId = "#result.data.id")
    public Result<OrderTask> createTask(@RequestBody CreateTaskRequest request,
                                       HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        OrderTask task = orderTaskService.createTask(request, operatorId);
        return ApiResponse.success("任务创建成功", task);
    }
    
    /**
     * 更新任务
     */
    @PutMapping("/{taskId}")
    @ActionLog(actionType = "UPDATE_TASK", description = "更新任务：#{#arg1.projectName}", targetId = "#arg0")
    public Result<OrderTask> updateTask(@PathVariable Long taskId,
                                       @RequestBody UpdateTaskRequest request,
                                       HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        OrderTask task = orderTaskService.updateTask(taskId, request, operatorId);
        return ApiResponse.success("任务更新成功", task);
    }
    
    /**
     * 删除任务
     */
    @DeleteMapping("/{taskId}")
    @ActionLog(actionType = "DELETE_TASK", description = "删除任务ID：#{#arg0}", targetId = "#arg0")
    public Result<Void> deleteTask(@PathVariable Long taskId,
                                  HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        orderTaskService.deleteTask(taskId, operatorId);
        return ApiResponse.success("任务删除成功");
    }

    /**
     * 批量删除任务
     */
    @DeleteMapping("/batch")
    @ActionLog(actionType = "BATCH_DELETE_TASK", description = "批量删除任务，数量：#{#arg0.size()}")
    public Result<Void> batchDeleteTasks(@RequestBody List<Long> taskIds,
                                        HttpServletRequest httpRequest) {
        if (taskIds == null || taskIds.isEmpty()) {
            return ApiResponse.badRequest("请选择要删除的任务");
        }
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        orderTaskService.batchDeleteTasks(taskIds, operatorId);
        return ApiResponse.success("批量删除成功");
    }
    
    /**
     * 分页查询任务列表
     */
    @GetMapping
    public Result<IPage<TaskListResponse>> getTaskList(TaskQueryRequest query) {
        IPage<TaskListResponse> taskList = orderTaskService.getTaskList(query);
        return ApiResponse.success(taskList);
    }
    
    /**
     * 获取任务详情
     */
    @GetMapping("/{taskId}")
    public Result<TaskListResponse> getTaskDetail(@PathVariable Long taskId) {
        TaskListResponse task = orderTaskService.getTaskDetail(taskId);
        return ApiResponse.success(task);
    }
    
    /**
     * 更新任务状态
     */
    @PutMapping("/{taskId}/status")
    public Result<Void> updateTaskStatus(@PathVariable Long taskId,
                                        @RequestParam Integer status,
                                        HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        orderTaskService.updateTaskStatus(taskId, status, operatorId);
        return ApiResponse.success("任务状态更新成功");
    }
    
    /**
     * 分配任务
     */
    @PutMapping("/{taskId}/assign")
    public Result<Void> assignTask(@PathVariable Long taskId,
                                  @RequestParam(required = false) Long assignedUserId,
                                  HttpServletRequest httpRequest) {
        Long operatorId = (Long) httpRequest.getAttribute("userId");
        orderTaskService.assignTask(taskId, assignedUserId, operatorId);
        return ApiResponse.success("任务分配成功");
    }
}
