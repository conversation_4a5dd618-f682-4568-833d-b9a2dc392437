<template>
  <div class="login-container">
    <!-- 简化的背景 -->
    <div class="simple-background"></div>

    <!-- 登录卡片 -->
    <div class="login-card-container">
      <div class="card-glow"></div>
      <el-card class="login-card">
        <template #header>
          <div class="card-header">
            <!-- Logo区域 -->
            <div class="logo-section">
              <div class="logo-wrapper">
                <img src="/haipao.svg" alt="海泡订单系统" class="header-logo" />
                <div class="logo-ripple"></div>
              </div>
            </div>
            <h2 class="system-title">海泡订单系统</h2>
            <p class="system-subtitle">深海般的稳定，浪潮般的高效</p>
          </div>
        </template>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="rules"
          label-width="0"
          size="large"
          class="login-form"
        >
          <el-form-item prop="username" class="form-item">
            <div class="input-container">
              <div class="input-icon">
                <el-icon><User /></el-icon>
              </div>
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                class="ocean-input"
              />
              <div class="input-wave"></div>
            </div>
          </el-form-item>
          
          <el-form-item prop="password" class="form-item">
            <div class="input-container">
              <div class="input-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
                class="ocean-input"
                @keyup.enter="handleLogin"
              />
              <div class="input-wave"></div>
            </div>
          </el-form-item>
          
          <el-form-item class="form-item remember-item">
            <el-checkbox v-model="loginForm.rememberMe" class="ocean-checkbox">
              <span class="checkbox-text">记住我</span>
            </el-checkbox>
          </el-form-item>
          
          <el-form-item class="form-item">
            <el-button
              type="primary"
              class="ocean-button"
              :loading="loading"
              @click="handleLogin"
            >
              <span v-if="!loading">登录</span>
              <span v-else>正在验证...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 海流样式生成
const getCurrentStyle = (index) => {
  const width = Math.random() * 300 + 200
  const top = Math.random() * 100
  const delay = Math.random() * 10
  const duration = Math.random() * 8 + 12
  
  return {
    width: width + 'px',
    top: top + '%',
    animationDelay: delay + 's',
    animationDuration: duration + 's'
  }
}

// 粒子样式生成
const getParticleStyle = (index) => {
  const size = Math.random() * 4 + 2
  const left = Math.random() * 100
  const top = Math.random() * 100
  const delay = Math.random() * 8
  const duration = Math.random() * 6 + 8
  
  return {
    width: size + 'px',
    height: size + 'px',
    left: left + '%',
    top: top + '%',
    animationDelay: delay + 's',
    animationDuration: duration + 's'
  }
}

// 气泡样式生成
const getBubbleStyle = (index) => {
  const size = Math.random() * 15 + 8
  const left = Math.random() * 100
  const delay = Math.random() * 6
  const duration = Math.random() * 4 + 6
  
  return {
    width: size + 'px',
    height: size + 'px',
    left: left + '%',
    animationDelay: delay + 's',
    animationDuration: duration + 's'
  }
}

// 光线样式生成
const getRayStyle = (index) => {
  const left = (index * 20) + Math.random() * 15
  const delay = Math.random() * 3
  const duration = Math.random() * 2 + 4
  
  return {
    left: left + '%',
    animationDelay: delay + 's',
    animationDuration: duration + 's'
  }
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const result = await userStore.login(loginForm)
        if (result.success) {
          router.push('/')
        }
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  overflow: hidden;
}

/* 简化的背景 */
.simple-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    #0c1e3d 0%, 
    #1e3a8a 25%, 
    #1e40af 50%, 
    #0369a1 75%, 
    #0284c7 100%);
  z-index: 1;
}

/* 登录卡片容器 */
.login-card-container {
  position: relative;
  z-index: 10;
  width: 450px;
  max-width: 90vw;
}

.card-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, transparent 70%);
  border-radius: 20px;
  animation: cardGlow 4s ease-in-out infinite alternate;
  z-index: -1;
}

@keyframes cardGlow {
  0% { 
    transform: scale(1);
    opacity: 0.5;
  }
  100% { 
    transform: scale(1.02);
    opacity: 0.8;
  }
}

.login-card {
  background: rgba(15, 23, 42, 0.85);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.login-card :deep(.el-card__header) {
  background: rgba(30, 58, 138, 0.2);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 30px 30px 25px;
}

.login-card :deep(.el-card__body) {
  padding: 30px;
}

/* 卡片头部 */
.card-header {
  text-align: center;
  color: white;
}

.logo-section {
  margin-bottom: 20px;
  position: relative;
}

.logo-wrapper {
  position: relative;
  display: inline-block;
}

.header-logo {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
  animation: logoFloat 4s ease-in-out infinite;
}

.logo-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border: 2px solid rgba(59, 130, 246, 0.4);
  border-radius: 50%;
  animation: ripple 3s ease-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.system-title {
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  font-weight: 300;
  letter-spacing: 2px;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.system-subtitle {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.7);
}

@keyframes titleGlow {
  0% { 
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
  100% { 
    text-shadow: 0 0 30px rgba(59, 130, 246, 1);
  }
}

/* 表单样式 */
.login-form {
  margin-top: 10px;
}

.form-item {
  margin-bottom: 25px;
}

.form-item :deep(.el-form-item__content) {
  width: 100%;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(30, 58, 138, 0.15);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 12px;
  padding: 0 20px;
  height: 55px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  overflow: hidden;
  width: 100%;
}

.input-container:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(30, 58, 138, 0.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.15);
}

.input-container:focus-within {
  border-color: rgba(59, 130, 246, 0.6);
  background: rgba(30, 58, 138, 0.25);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.25);
}

/* 覆盖Element Plus错误状态样式 */
.form-item.is-error .input-container {
  border-color: rgba(59, 130, 246, 0.25) !important;
  background: rgba(30, 58, 138, 0.15) !important;
  box-shadow: none !important;
}

.form-item.is-error .input-container:hover {
  border-color: rgba(59, 130, 246, 0.4) !important;
  background: rgba(30, 58, 138, 0.2) !important;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.15) !important;
}

.form-item.is-error .input-container:focus-within {
  border-color: rgba(59, 130, 246, 0.6) !important;
  background: rgba(30, 58, 138, 0.25) !important;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.25) !important;
}

/* 隐藏Element Plus的错误状态样式 */
.form-item :deep(.el-form-item__error) {
  display: none !important;
}

.form-item :deep(.el-input.is-error .el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
}

.form-item :deep(.el-input__wrapper.is-error) {
  border: none !important;
  box-shadow: none !important;
}

.ocean-input :deep(.el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 0 !important;
  height: auto !important;
  width: 100% !important;
}

.ocean-input :deep(.el-input__wrapper:hover),
.ocean-input :deep(.el-input__wrapper.is-focus),
.ocean-input :deep(.el-input__wrapper.is-error) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 15px;
  color: rgba(59, 130, 246, 0.8);
  font-size: 18px;
  transition: color 0.3s ease;
  flex-shrink: 0;
}

.input-container:focus-within .input-icon {
  color: rgba(59, 130, 246, 1);
}

.ocean-input {
  flex: 1;
  width: 100%;
}

.ocean-input :deep(.el-input__wrapper) {
  background: transparent;
  border: none;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  height: auto;
  width: 100%;
}

.ocean-input :deep(.el-input__wrapper:hover),
.ocean-input :deep(.el-input__wrapper.is-focus) {
  background: transparent;
  border: none;
  box-shadow: none;
}

.ocean-input :deep(.el-input__inner) {
  color: white !important;
  background: transparent !important;
  border: none;
  padding: 0;
  height: 55px;
  line-height: 55px;
  font-size: 16px;
  width: 100%;
  /* 强制覆盖自动填充样式 */
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.ocean-input :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.5) !important;
  font-size: 15px;
}

.ocean-input :deep(.el-input__suffix) {
  color: rgba(59, 130, 246, 0.8);
}

.ocean-input :deep(.el-input__suffix .el-input__suffix-inner) {
  color: rgba(59, 130, 246, 0.8);
}

.ocean-input :deep(.el-input__suffix .el-input__password) {
  color: rgba(59, 130, 246, 0.8);
}

.input-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(59, 130, 246, 0.8) 50%, 
    transparent 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 50px 50px;
}

.input-container:focus-within .input-wave {
  transform: scaleX(1);
}

/* 记住我选项 */
.remember-item {
  margin-bottom: 25px;
  margin-left: 5px;
}

.remember-item :deep(.el-form-item__content) {
  line-height: normal;
}

.ocean-checkbox {
  display: flex;
  align-items: center;
}

.ocean-checkbox :deep(.el-checkbox__input) {
  margin-right: 10px;
}

.ocean-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: rgba(59, 130, 246, 0.8);
  border-color: rgba(59, 130, 246, 0.8);
}

.ocean-checkbox :deep(.el-checkbox__inner) {
  background: rgba(30, 58, 138, 0.3);
  border-color: rgba(59, 130, 246, 0.4);
  border-radius: 4px;
  width: 18px;
  height: 18px;
}

.ocean-checkbox :deep(.el-checkbox__inner::after) {
  border-color: white;
  border-width: 2px;
}

.checkbox-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  font-weight: 400;
}

/* 登录按钮 */
.ocean-button {
  width: 100%;
  height: 55px;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.9) 0%, 
    rgba(14, 165, 233, 1) 50%, 
    rgba(6, 182, 212, 0.9) 100%);
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.ocean-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%);
  transition: left 0.6s ease;
}

.ocean-button:hover::before {
  left: 100%;
}

.ocean-button:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 12px 35px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.ocean-button:active {
  transform: translateY(-1px);
}

.ocean-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 加载状态 */
.ocean-button.is-loading {
  pointer-events: none;
}

.ocean-button.is-loading::before {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card-container {
    width: 350px;
  }
  
  .login-card :deep(.el-card__header) {
    padding: 25px 20px 20px;
  }
  
  .login-card :deep(.el-card__body) {
    padding: 25px 20px;
  }
  
  .system-title {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }
  
  .system-subtitle {
    font-size: 0.8rem;
  }
  
  .header-logo {
    width: 70px;
    height: 70px;
  }
  
  .logo-ripple {
    width: 90px;
    height: 90px;
  }
}

@media (max-width: 480px) {
  .login-card-container {
    width: 320px;
  }
  
  .system-title {
    font-size: 1.3rem;
  }
  
  .header-logo {
    width: 60px;
    height: 60px;
  }
}

/* 覆盖浏览器自动填充样式 */
.ocean-input :deep(.el-input__inner:-webkit-autofill),
.ocean-input :deep(.el-input__inner:-webkit-autofill:hover),
.ocean-input :deep(.el-input__inner:-webkit-autofill:focus),
.ocean-input :deep(.el-input__inner:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: white !important;
  background-color: transparent !important;
  background-image: none !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Firefox 自动填充样式 */
.ocean-input :deep(.el-input__inner:-moz-autofill) {
  background-color: transparent !important;
  color: white !important;
  box-shadow: none !important;
}

/* 通用自动填充样式覆盖 */
.ocean-input :deep(.el-input__inner:autofill) {
  background-color: transparent !important;
  color: white !important;
  -webkit-text-fill-color: white !important;
  box-shadow: none !important;
}

/* 额外的自动填充样式覆盖 - 针对不同浏览器 */
.ocean-input :deep(input:-webkit-autofill),
.ocean-input :deep(input:-webkit-autofill:hover),
.ocean-input :deep(input:-webkit-autofill:focus),
.ocean-input :deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: white !important;
  background-color: transparent !important;
  background-image: none !important;
  transition: background-color 5000s ease-in-out 0s !important;
  caret-color: white !important;
}

/* 强制覆盖任何可能的自动填充背景 */
.ocean-input :deep(input) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  color: white !important;
  -webkit-text-fill-color: white !important;
}
</style>



