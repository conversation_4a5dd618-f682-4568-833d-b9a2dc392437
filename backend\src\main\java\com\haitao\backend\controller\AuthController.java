package com.haitao.backend.controller;

import com.haitao.backend.annotation.LoginLog;
import com.haitao.backend.common.ApiResponse;
import com.haitao.backend.common.ErrorMessages;
import com.haitao.backend.common.Result;
import com.haitao.backend.dto.LoginRequest;
import com.haitao.backend.dto.LoginResponse;
import com.haitao.backend.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping("/login")
    @LoginLog(description = "用户登录")
    public Result<LoginResponse> login(@RequestBody LoginRequest request) {
        try {
            LoginResponse response = userService.login(request);
            return ApiResponse.success(ErrorMessages.LOGIN_SUCCESS, response);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}


