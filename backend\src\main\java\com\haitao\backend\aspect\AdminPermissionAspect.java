package com.haitao.backend.aspect;

import com.haitao.backend.annotation.RequireAdmin;
import com.haitao.backend.common.ErrorMessages;
import com.haitao.backend.exception.BusinessException;
import com.haitao.backend.service.UserService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;

/**
 * 管理员权限验证切面
 */
@Aspect
@Component
public class AdminPermissionAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminPermissionAspect.class);
    
    @Autowired
    private UserService userService;
    
    @Before("@annotation(requireAdmin)")
    public void checkAdminPermission(JoinPoint joinPoint, RequireAdmin requireAdmin) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new BusinessException(ErrorMessages.SYSTEM_ERROR);
        }

        HttpServletRequest request = attributes.getRequest();
        Long userId = (Long) request.getAttribute("userId");

        if (userId == null) {
            throw new BusinessException(ErrorMessages.USER_NOT_LOGGED_IN);
        }

        if (!userService.isAdmin(userId)) {
            logger.warn("用户 {} 尝试访问需要管理员权限的接口: {}", userId, joinPoint.getSignature().getName());
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }
        
        logger.debug("管理员权限验证通过: userId={}, method={}", userId, joinPoint.getSignature().getName());
    }
}
