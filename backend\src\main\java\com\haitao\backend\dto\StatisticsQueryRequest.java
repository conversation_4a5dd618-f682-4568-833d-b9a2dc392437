package com.haitao.backend.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 统计查询请求DTO
 */
@Data
public class StatisticsQueryRequest {
    
    /** 时间维度：year=年，month=月，week=周，day=日 */
    private String timeDimension;
    
    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /** 指定用户ID（可选，为空则统计所有用户） */
    private Long userId;
    
    /** 任务状态筛选（可选，为空则统计所有状态） */
    private Integer status;
    
    /** 是否只统计已结算的任务（true=只统计已结算，false=统计已完成和已结算） */
    private Boolean onlySettled;
    
    /** 统计类型：income=收入统计，trend=趋势分析，ranking=用户排行 */
    private String statisticsType;
    
    /** 排序字段（用于排行榜）：totalAmount=总金额，netIncome=实得收入，taskCount=任务数量 */
    private String sortBy;
    
    /** 排序方向：asc=升序，desc=降序 */
    private String sortOrder;
    
    /** 返回记录数限制（用于排行榜） */
    private Integer limit;

    /** 是否包含管理员（用于排行榜）：true=包含管理员，false=仅普通用户 */
    private Boolean includeAdmin;
}
