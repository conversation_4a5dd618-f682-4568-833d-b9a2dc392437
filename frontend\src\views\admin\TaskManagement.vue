<template>
  <div class="task-management">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索项目名称或订单编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="待处理" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="客户已取消" :value="3" />
            <el-option label="待结算" :value="4" />
            <el-option label="已结算" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="分配成员">
          <el-select v-model="searchForm.assignedUserId" placeholder="选择成员" clearable style="width: 150px">
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.realName || user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTaskList" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button
            v-if="selectedTasks.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除 ({{ selectedTasks.length }})
          </el-button>
          <el-button type="success" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 任务列表 -->
    <div class="table-section">
      <el-table
        :data="taskList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
        :header-cell-style="{ textAlign: 'center', fontWeight: 'bold' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNumber" label="订单编号" width="140" />
        <el-table-column prop="projectName" label="项目名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="totalPrice" label="总价" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.totalPrice?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="commissionRate" label="抽成比例" width="100" align="center">
          <template #default="{ row }">
            {{ (row.commissionRate * 100).toFixed(1) }}%
          </template>
        </el-table-column>
        <el-table-column prop="netIncome" label="实得收入" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.netIncome?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="assignedUserName" label="分配成员" width="120">
          <template #default="{ row }">
            {{ row.assignedUserName || '未分配' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="140" align="center">
          <template #default="{ row }">
            <div class="status-cell">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
                style="margin-right: 8px;"
              >
                {{ row.statusName }}
              </el-tag>
              <el-dropdown
                @command="(command) => handleStatusChange(row, command)"
                trigger="click"
                size="small"
              >
                <el-button type="primary" size="small" text>
                  <el-icon><Switch /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="status in getAvailableStatuses(row.status)"
                      :key="status.value"
                      :command="status.value"
                    >
                      <el-tag :type="getStatusType(status.value)" size="small">
                        {{ status.label }}
                      </el-tag>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="orderTime" label="接单时间" width="160" />
        <el-table-column prop="deadline" label="截止时间" width="160">
          <template #default="{ row }">
            {{ row.deadline || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="info" size="small" @click="handleDetail(row)">
                详情
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑任务对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="taskForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="taskForm.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单编号" prop="orderNumber">
              <el-input
                v-model="taskForm.orderNumber"
                placeholder="请输入订单编号（留空自动生成）"
                clearable
              />
              <div class="form-tip">留空将自动生成格式：ORD20250727001</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目总价" prop="totalPrice">
              <el-input-number
                v-model="taskForm.totalPrice"
                :min="0.01"
                :precision="2"
                placeholder="请输入项目总价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="抽成比例" prop="commissionRate">
              <el-input-number
                v-model="taskForm.commissionRate"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                placeholder="请输入抽成比例"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="派单员抽成" prop="dispatcherFee">
              <el-input-number
                v-model="taskForm.dispatcherFee"
                :min="0"
                :precision="2"
                placeholder="自动计算"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分配成员" prop="assignedUserId">
              <el-select v-model="taskForm.assignedUserId" placeholder="选择分配成员" style="width: 100%">
                <el-option label="暂不分配" :value="null" />
                <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.realName || user.username"
                    :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接单时间" prop="orderTime">
              <el-date-picker
                v-model="taskForm.orderTime"
                type="datetime"
                placeholder="选择接单时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="taskForm.deadline"
                type="datetime"
                placeholder="选择截止时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDeadlineDate"
                :disabled-time="disabledDeadlineTime"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12" v-if="isEdit">
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="taskForm.status" placeholder="选择任务状态" style="width: 100%">
                <el-option label="待处理" :value="0" />
                <el-option label="进行中" :value="1" />
                <el-option label="已完成" :value="2" />
                <el-option label="客户已取消" :value="3" />
                <el-option label="待结算" :value="4" />
                <el-option label="已结算" :value="5" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注信息" prop="remarks">
          <el-input
            v-model="taskForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 收入计算预览 -->
        <div v-if="taskForm.totalPrice && taskForm.commissionRate !== null" class="income-preview">
          <el-alert
            title="收入计算预览"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>项目总价：¥{{ taskForm.totalPrice?.toFixed(2) }}</p>
              <p>抽成比例：{{ (taskForm.commissionRate * 100).toFixed(1) }}%</p>
              <p>抽成金额：¥{{ (taskForm.totalPrice * taskForm.commissionRate).toFixed(2) }}</p>
              <p>派单员抽成：¥{{ taskForm.dispatcherFee?.toFixed(2) }} <span class="auto-calc-note"></span></p>
              <p><strong>实得收入：¥{{ (taskForm.totalPrice * (1 - taskForm.commissionRate)).toFixed(2) }}</strong></p>
            </template>
          </el-alert>
        </div>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      width="750px"
      :close-on-click-modal="false"
      class="detail-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="header-content">
            <el-icon class="header-icon"><Document /></el-icon>
            <div class="header-text">
              <h2>订单详情</h2>
              <p>{{ currentTaskDetail?.orderNumber }}</p>
            </div>
          </div>
          <el-tag
            v-if="currentTaskDetail"
            :type="getStatusType(currentTaskDetail.status)"
            size="large"
            class="status-tag"
          >
            {{ currentTaskDetail.statusName }}
          </el-tag>
        </div>
      </template>

      <div v-if="currentTaskDetail" class="task-detail">
        <!-- 核心信息卡片 -->
        <div class="core-info-card">
          <div class="project-info">
            <div class="project-title">
              <el-icon class="project-icon"><Briefcase /></el-icon>
              <h3>{{ currentTaskDetail.projectName }}</h3>
            </div>
            <div class="project-meta">
              <span class="order-number">#{{ currentTaskDetail.orderNumber }}</span>
              <span class="assigned-user">
                <el-icon><User /></el-icon>
                {{ currentTaskDetail.assignedUserName || '未分配' }}
              </span>
            </div>
          </div>

          <div class="financial-summary">
            <div class="amount-item total">
              <div class="amount-label">项目总价</div>
              <div class="amount-value">¥{{ currentTaskDetail.totalPrice?.toFixed(2) }}</div>
            </div>
            <div class="amount-item commission">
              <div class="amount-label">抽成比例</div>
              <div class="amount-value">{{ (currentTaskDetail.commissionRate * 100).toFixed(1) }}%</div>
            </div>
            <div class="amount-item net">
              <div class="amount-label">实得收入</div>
              <div class="amount-value highlight">¥{{ currentTaskDetail.netIncome?.toFixed(2) }}</div>
            </div>
          </div>
        </div>

        <!-- 详细信息网格 -->
        <div class="detail-grid">
          <!-- 财务明细 -->
          <div class="detail-card financial-detail">
            <div class="card-header">
              <el-icon class="card-icon"><Money /></el-icon>
              <h4>财务明细</h4>
            </div>
            <div class="financial-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label">抽成金额</span>
                <span class="breakdown-value commission">¥{{ currentTaskDetail.commissionAmount?.toFixed(2) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label">派单员抽成</span>
                <span class="breakdown-value dispatcher">¥{{ currentTaskDetail.dispatcherFee?.toFixed(2) }}</span>
              </div>
              <div class="breakdown-divider"></div>
              <div class="breakdown-item total">
                <span class="breakdown-label">实得收入</span>
                <span class="breakdown-value net">¥{{ currentTaskDetail.netIncome?.toFixed(2) }}</span>
              </div>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="detail-card time-info">
            <div class="card-header">
              <el-icon class="card-icon"><Clock /></el-icon>
              <h4>时间信息</h4>
            </div>
            <div class="time-items">
              <div class="time-item">
                <div class="time-label">
                  <el-icon><Calendar /></el-icon>
                  接单时间
                </div>
                <div class="time-value">{{ currentTaskDetail.orderTime }}</div>
              </div>
              <div class="time-item">
                <div class="time-label">
                  <el-icon><Calendar /></el-icon>
                  截止时间
                </div>
                <div class="time-value">{{ currentTaskDetail.deadline || '未设置' }}</div>
              </div>
              <div class="time-item">
                <div class="time-label">
                  <el-icon><Calendar /></el-icon>
                  创建时间
                </div>
                <div class="time-value">{{ currentTaskDetail.createTime }}</div>
              </div>
            </div>
          </div>

          <!-- 其他信息 -->
          <div class="detail-card other-info">
            <div class="card-header">
              <el-icon class="card-icon"><InfoFilled /></el-icon>
              <h4>其他信息</h4>
            </div>
            <div class="info-items">
              <div class="info-item">
                <span class="info-label">创建人</span>
                <span class="info-value">{{ currentTaskDetail.createdByName }}</span>
              </div>
              <div class="info-item remarks-item">
                <span class="info-label">备注信息</span>
                <div class="remarks-content">
                  {{ currentTaskDetail.remarks || '无备注信息' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="detailDialogVisible = false">
            关闭
          </el-button>
          <el-button type="primary" size="large" @click="handleEditFromDetail">
            <el-icon><Edit /></el-icon>
            编辑任务
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Switch, Document, Briefcase, User, Clock, Calendar, Money, InfoFilled, Edit, Delete } from '@element-plus/icons-vue'
import { taskApi } from '../../api/task'
import { userApi } from '../../api/user'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const taskList = ref([])
const selectedTasks = ref([])
const userList = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentTaskId = ref(null)

// 详情对话框相关
const detailDialogVisible = ref(false)
const currentTaskDetail = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null,
  assignedUserId: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 任务表单
const taskForm = reactive({
  projectName: '',
  orderNumber: '',
  totalPrice: null,
  commissionRate: null,
  dispatcherFee: 0,
  assignedUserId: null,
  orderTime: '',
  deadline: '',
  status: 0,
  remarks: ''
})

// 表单引用
const formRef = ref()

// 对话框标题
const dialogTitle = computed(() => isEdit.value ? '编辑任务' : '新建任务')

// 计算派单员抽成（等于抽成金额）
const calculatedDispatcherFee = computed(() => {
  if (taskForm.totalPrice && taskForm.commissionRate !== null) {
    return Number((taskForm.totalPrice * taskForm.commissionRate).toFixed(2))
  }
  return 0
})

// 监听总价和抽成比例变化，自动更新派单员抽成
watch([() => taskForm.totalPrice, () => taskForm.commissionRate], () => {
  taskForm.dispatcherFee = calculatedDispatcherFee.value
}, { immediate: true })

// 截止时间验证函数
const validateDeadline = (rule, value, callback) => {
  if (!value) {
    // 截止时间不是必填项，可以为空
    callback()
    return
  }

  if (!taskForm.orderTime) {
    callback(new Error('请先选择接单时间'))
    return
  }

  const orderTime = new Date(taskForm.orderTime)
  const deadline = new Date(value)

  if (deadline <= orderTime) {
    callback(new Error('截止时间必须晚于接单时间'))
    return
  }

  callback()
}

// 监听接单时间变化，重新验证截止时间
watch(() => taskForm.orderTime, () => {
  if (taskForm.deadline && formRef.value) {
    // 当接单时间变化时，重新验证截止时间字段
    formRef.value.validateField('deadline')
  }
})

// 禁用截止日期（只禁用接单日期之前的日期）
const disabledDeadlineDate = (time) => {
  if (!taskForm.orderTime) {
    return false
  }
  const orderDate = new Date(taskForm.orderTime)
  const targetDate = new Date(time)

  // 只禁用接单日期之前的日期，接单日期当天及之后都可以选择
  return targetDate < orderDate.setHours(0, 0, 0, 0)
}

// 禁用截止时间（只在接单当天禁用接单时间之前的时间）
const disabledDeadlineTime = (time) => {
  if (!taskForm.orderTime) {
    return {}
  }

  const orderTime = new Date(taskForm.orderTime)
  const targetTime = new Date(time)

  // 只有在接单当天才需要禁用接单时间之前的时间
  if (targetTime.toDateString() !== orderTime.toDateString()) {
    return {} // 其他日期不禁用任何时间
  }

  // 接单当天，禁用接单时间之前的时间
  const orderHour = orderTime.getHours()
  const orderMinute = orderTime.getMinutes()

  return {
    disabledHours: () => {
      const hours = []
      for (let i = 0; i < orderHour; i++) {
        hours.push(i)
      }
      return hours
    },
    disabledMinutes: (hour) => {
      if (hour === orderHour) {
        const minutes = []
        for (let i = 0; i <= orderMinute; i++) {
          minutes.push(i)
        }
        return minutes
      }
      return []
    }
  }
}

// 表单验证规则
const formRules = computed(() => ({
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { max: 100, message: '项目名称长度不能超过100个字符', trigger: 'blur' }
  ],
  orderNumber: [
    ...(isEdit.value ? [{ required: true, message: '请输入订单编号', trigger: 'blur' }] : []),
    { max: 100, message: '订单编号长度不能超过100个字符', trigger: 'blur' }
  ],
  totalPrice: [
    { required: true, message: '请输入项目总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '项目总价必须大于0', trigger: 'blur' }
  ],
  commissionRate: [
    { required: true, message: '请输入抽成比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '抽成比例必须在0-1之间', trigger: 'blur' }
  ],
  orderTime: [
    { required: true, message: '请选择接单时间', trigger: 'change' }
  ],
  deadline: [
    {
      validator: validateDeadline,
      trigger: 'change'
    }
  ]
}))

// 获取状态标签类型
const getStatusType = (status) => {
  const statusTypes = {
    0: '',
    1: 'warning',
    2: 'success',
    3: 'danger',
    4: 'info',
    5: 'success'
  }
  return statusTypes[status] || ''
}

// 获取可切换的状态列表
const getAvailableStatuses = (currentStatus) => {
  const allStatuses = [
    { value: 0, label: '待处理' },
    { value: 1, label: '进行中' },
    { value: 2, label: '已完成' },
    { value: 3, label: '客户已取消' },
    { value: 4, label: '待结算' },
    { value: 5, label: '已结算' }
  ]

  // 根据当前状态返回可切换的状态
  switch (currentStatus) {
    case 0: // 待处理 -> 可切换到进行中、客户已取消
      return allStatuses.filter(s => [1, 3].includes(s.value))
    case 1: // 进行中 -> 可切换到已完成、客户已取消
      return allStatuses.filter(s => [2, 3].includes(s.value))
    case 2: // 已完成 -> 可切换到待结算
      return allStatuses.filter(s => [4].includes(s.value))
    case 3: // 客户已取消 -> 可切换到待处理（重新开始）
      return allStatuses.filter(s => [0].includes(s.value))
    case 4: // 待结算 -> 可切换到已结算
      return allStatuses.filter(s => [5].includes(s.value))
    case 5: // 已结算 -> 不可切换
      return []
    default:
      return []
  }
}

// 处理状态切换
const handleStatusChange = async (row, newStatus) => {
  try {
    const statusNames = {
      0: '待处理',
      1: '进行中',
      2: '已完成',
      3: '客户已取消',
      4: '待结算',
      5: '已结算'
    }

    await ElMessageBox.confirm(
      `确定要将任务"${row.projectName}"的状态切换为"${statusNames[newStatus]}"吗？`,
      '状态切换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用更新接口
    const updateData = {
      projectName: row.projectName,
      orderNumber: row.orderNumber,
      totalPrice: row.totalPrice,
      commissionRate: row.commissionRate,
      dispatcherFee: row.dispatcherFee,
      assignedUserId: row.assignedUserId,
      orderTime: row.orderTime,
      deadline: row.deadline,
      status: newStatus,
      remarks: row.remarks
    }

    const response = await taskApi.updateTask(row.id, updateData)
    if (response.code === 200) {
      ElMessage.success(`状态已切换为"${statusNames[newStatus]}"`)
      getTaskList() // 刷新列表
    } else {
      ElMessage.error(response.message || '状态切换失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('状态切换失败，请稍后重试')
    }
  }
}

// 获取任务列表
const getTaskList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    }

    const response = await taskApi.getTaskList(params)
    if (response.code === 200) {
      taskList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取用户列表
const getUserList = async () => {
  try {
    const response = await userApi.getUserList({ page: 1, size: 100 })
    if (response.code === 200) {
      userList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: null,
    assignedUserId: null
  })
  pagination.page = 1
  getTaskList()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  getTaskList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.page = page
  getTaskList()
}

// 新建任务
const handleCreate = () => {
  isEdit.value = false
  currentTaskId.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑任务
const handleEdit = async (row) => {
  try {
    isEdit.value = true
    currentTaskId.value = row.id

    // 获取任务详情
    const response = await taskApi.getTaskDetail(row.id)
    if (response.code === 200) {
      const task = response.data
      Object.assign(taskForm, {
        projectName: task.projectName,
        orderNumber: task.orderNumber,
        totalPrice: task.totalPrice,
        commissionRate: task.commissionRate,
        assignedUserId: task.assignedUserId,
        orderTime: task.orderTime,
        deadline: task.deadline,
        status: task.status,
        remarks: task.remarks || ''
      })
      // 派单员抽成会通过watch自动计算，不需要手动设置
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
  }
}

// 查看详情
const handleDetail = async (row) => {
  try {
    loading.value = true
    const response = await taskApi.getTaskDetail(row.id)
    if (response.code === 200) {
      currentTaskDetail.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取任务详情失败')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 删除任务
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务"${row.projectName}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await taskApi.deleteTask(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getTaskList()
    }
  } catch {
    // 用户取消
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedTasks.value = selection
}

// 批量删除任务
const handleBatchDelete = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要删除的任务')
    return
  }

  try {
    const taskNames = selectedTasks.value.map(task => task.projectName).join('、')
    await ElMessageBox.confirm(
      `确定要删除以下任务吗？此操作不可恢复！\n\n${taskNames}`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    const taskIds = selectedTasks.value.map(task => task.id)
    const response = await taskApi.batchDeleteTasks(taskIds)

    if (response.code === 200) {
      ElMessage.success(`成功删除 ${selectedTasks.value.length} 个任务`)
      selectedTasks.value = []
      getTaskList()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败，请稍后重试')
    }
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(taskForm, {
    projectName: '',
    orderNumber: '',
    totalPrice: null,
    commissionRate: null,
    assignedUserId: null,
    orderTime: '',
    deadline: '',
    status: 0,
    remarks: ''
  })
  // 派单员抽成会通过watch自动计算为0
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEdit.value) {
      // 编辑任务
      const response = await taskApi.updateTask(currentTaskId.value, taskForm)
      if (response.code === 200) {
        ElMessage.success('任务更新成功')
        dialogVisible.value = false
        getTaskList()
      } else {
        ElMessage.error(response.message || '任务更新失败')
      }
    } else {
      // 创建任务
      const response = await taskApi.createTask(taskForm)
      if (response.code === 200) {
        ElMessage.success('任务创建成功')
        dialogVisible.value = false
        getTaskList()
      } else {
        ElMessage.error(response.message || '任务创建失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请检查输入信息')
  } finally {
    submitting.value = false
  }
}

// 从详情页面编辑任务
const handleEditFromDetail = () => {
  detailDialogVisible.value = false
  handleEdit(currentTaskDetail.value)
}

// 页面加载时获取数据
onMounted(() => {
  getTaskList()
  getUserList()
})
</script>

<style scoped>
.task-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.income-preview {
  margin-top: 20px;
}

.income-preview p {
  margin: 4px 0;
  font-size: 14px;
}

.income-preview strong {
  color: #059669;
  font-weight: 600;
}

.auto-calc-note {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.status-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-cell .el-dropdown {
  margin-left: 4px;
}

/* 详情对话框样式 */
.detail-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.detail-dialog :deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

.detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.detail-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #2dd4bf 0%, #0ea5e9 100%);
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  opacity: 0.9;
}

.header-text h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-text p {
  margin: 2px 0 0 0;
  opacity: 0.8;
  font-size: 13px;
}

.status-tag {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white !important;
}

/* 详情内容 */
.task-detail {
  padding: 20px;
  background: #f8fafc;
  min-height: 400px;
}

/* 核心信息卡片 */
.core-info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.project-info {
  margin-bottom: 16px;
}

.project-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.project-icon {
  font-size: 20px;
  color: #6366f1;
}

.project-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
}

.project-meta {
  display: flex;
  gap: 16px;
  align-items: center;
}

.order-number {
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
}

.assigned-user {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  font-weight: 500;
}

/* 财务摘要 */
.financial-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.amount-item {
  text-align: center;
  padding: 16px;
  border-radius: 10px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.amount-item.total {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.amount-item.commission {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.amount-item.net {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.amount-label {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 6px;
  font-weight: 500;
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
}

.amount-value.highlight {
  color: #059669;
}

/* 详情网格 */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

/* 详情卡片 */
.detail-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.detail-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.card-icon {
  font-size: 18px;
  color: #6366f1;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

/* 财务明细 */
.financial-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.breakdown-item.total {
  padding-top: 16px;
  border-top: 2px solid #e2e8f0;
  font-weight: 600;
}

.breakdown-label {
  color: #64748b;
  font-weight: 500;
}

.breakdown-value {
  font-weight: 600;
  font-size: 16px;
}

.breakdown-value.commission {
  color: #f59e0b;
}

.breakdown-value.dispatcher {
  color: #3b82f6;
}

.breakdown-value.net {
  color: #059669;
  font-size: 18px;
}

.breakdown-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 8px 0;
}

/* 时间信息 */
.time-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-item {
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #6366f1;
}

.time-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 13px;
}

.time-value {
  color: #1a202c;
  font-weight: 600;
  font-size: 14px;
}

/* 其他信息 */
.info-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.remarks-item {
  flex-direction: column;
  align-items: stretch;
}

.info-label {
  color: #64748b;
  font-weight: 500;
  min-width: 70px;
  font-size: 13px;
}

.info-value {
  color: #1a202c;
  font-weight: 600;
  font-size: 14px;
}

.remarks-content {
  background: #f8fafc;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  margin-top: 8px;
  color: #64748b;
  line-height: 1.5;
  min-height: 40px;
  font-size: 13px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 批量删除按钮样式 */
.batch-delete-btn {
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.batch-delete-btn .el-icon {
  margin-right: 4px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table tr:hover > td) {
  background: #f8fafc;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-dialog__title) {
  color: #1f2937;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-form-item__label) {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-management {
    padding: 12px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
