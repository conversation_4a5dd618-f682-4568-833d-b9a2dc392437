package com.haitao.backend.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

/**
 * 性能监控工具类
 * 用于开发环境监控方法执行时间，仅记录日志不暴露接口
 */
public class PerformanceUtil {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceUtil.class);

    // 是否启用性能监控（可通过配置控制）
    private static final boolean PERFORMANCE_MONITORING_ENABLED =
        Boolean.parseBoolean(System.getProperty("performance.monitoring.enabled", "true"));
    

    
    /**
     * 监控方法执行时间（开发环境使用）
     */
    public static <T> T monitor(String methodName, Supplier<T> supplier) {
        if (!PERFORMANCE_MONITORING_ENABLED) {
            return supplier.get();
        }

        long startTime = System.currentTimeMillis();
        try {
            T result = supplier.get();
            long executionTime = System.currentTimeMillis() - startTime;

            // 只记录日志，不存储统计信息
            if (executionTime > 2000) { // 2秒
                logger.warn("⚠️ 方法 {} 执行时间过长: {}ms", methodName, executionTime);
            } else if (executionTime > 500) { // 500毫秒
                logger.info("⏱️ 方法 {} 执行时间较长: {}ms", methodName, executionTime);
            } else if (logger.isDebugEnabled()) {
                logger.debug("✅ 方法 {} 执行时间: {}ms", methodName, executionTime);
            }

            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("❌ 方法 {} 执行异常，耗时: {}ms", methodName, executionTime, e);
            throw e;
        }
    }
    
    /**
     * 监控无返回值的方法（开发环境使用）
     */
    public static void monitor(String methodName, Runnable runnable) {
        monitor(methodName, () -> {
            runnable.run();
            return null;
        });
    }
}
