<template>
  <el-card class="statistics-card" :class="cardClass" shadow="hover">
    <div class="card-header">
      <div class="icon-wrapper" :style="{ backgroundColor: iconBgColor }">
        <el-icon :size="24" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-title">{{ title }}</div>
        <div class="card-value">{{ formattedValue }}</div>
        <div class="card-trend" v-if="trend !== null">
          <el-icon :color="trendColor">
            <ArrowUp v-if="trend > 0" />
            <ArrowDown v-if="trend < 0" />
            <Minus v-if="trend === 0" />
          </el-icon>
          <span :style="{ color: trendColor }">{{ Math.abs(trend) }}%</span>
        </div>
      </div>
    </div>
    <div class="card-footer" v-if="subtitle">
      <span class="subtitle">{{ subtitle }}</span>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { 
  Money, 
  TrendCharts, 
  User, 
  Document,
  ArrowUp,
  ArrowDown,
  Minus
} from '@element-plus/icons-vue'

const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    required: true
  },
  // 数值
  value: {
    type: [Number, String],
    required: true
  },
  // 副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 图标类型
  iconType: {
    type: String,
    default: 'money',
    validator: (value) => ['money', 'chart', 'user', 'document'].includes(value)
  },
  // 卡片类型（影响颜色主题）
  type: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  // 趋势百分比（正数表示上升，负数表示下降）
  trend: {
    type: Number,
    default: null
  },
  // 数值格式化类型
  format: {
    type: String,
    default: 'number',
    validator: (value) => ['number', 'currency', 'percentage'].includes(value)
  },
  // 是否显示动画
  animated: {
    type: Boolean,
    default: true
  }
})

// 图标映射
const iconMap = {
  money: Money,
  chart: TrendCharts,
  user: User,
  document: Document
}

// 计算图标组件
const icon = computed(() => iconMap[props.iconType])

// 颜色主题配置
const colorThemes = {
  primary: {
    iconBg: '#409EFF',
    iconColor: '#ffffff',
    cardClass: 'card-primary'
  },
  success: {
    iconBg: '#67C23A',
    iconColor: '#ffffff',
    cardClass: 'card-success'
  },
  warning: {
    iconBg: '#E6A23C',
    iconColor: '#ffffff',
    cardClass: 'card-warning'
  },
  danger: {
    iconBg: '#F56C6C',
    iconColor: '#ffffff',
    cardClass: 'card-danger'
  },
  info: {
    iconBg: '#909399',
    iconColor: '#ffffff',
    cardClass: 'card-info'
  }
}

// 计算样式
const iconBgColor = computed(() => colorThemes[props.type].iconBg)
const iconColor = computed(() => colorThemes[props.type].iconColor)
const cardClass = computed(() => colorThemes[props.type].cardClass)

// 趋势颜色
const trendColor = computed(() => {
  if (props.trend === null) return '#909399'
  if (props.trend > 0) return '#67C23A'
  if (props.trend < 0) return '#F56C6C'
  return '#909399'
})

// 格式化数值
const formattedValue = computed(() => {
  const value = props.value
  
  if (typeof value !== 'number') {
    return value
  }
  
  switch (props.format) {
    case 'currency':
      return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'percentage':
      return `${value.toFixed(2)}%`
    case 'number':
    default:
      return value.toLocaleString('zh-CN')
  }
})
</script>

<style scoped>
.statistics-card {
  height: 120px;
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 80px;
}

.icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-footer {
  height: 20px;
  display: flex;
  align-items: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
  margin-top: 8px;
}

.subtitle {
  font-size: 12px;
  color: #909399;
}

/* 主题样式 */
.card-primary {
  border-left: 4px solid #409EFF;
}

.card-success {
  border-left: 4px solid #67C23A;
}

.card-warning {
  border-left: 4px solid #E6A23C;
}

.card-danger {
  border-left: 4px solid #F56C6C;
}

.card-info {
  border-left: 4px solid #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-card {
    height: auto;
    min-height: 100px;
  }
  
  .card-header {
    height: auto;
    padding: 8px 0;
  }
  
  .icon-wrapper {
    width: 48px;
    height: 48px;
  }
  
  .card-value {
    font-size: 20px;
  }
}
</style>
