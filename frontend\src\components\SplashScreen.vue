<template>
  <div class="splash-screen" v-if="visible">
    <!-- 简化背景 -->
    <div class="simple-background"></div>
    
    <div class="splash-content">
      <!-- Logo区域 -->
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="/haipao.svg" alt="海泡订单系统" class="logo" />
          <div class="logo-glow"></div>
        </div>
      </div>
      
      <!-- 品牌名称 -->
      <div class="brand-container">
        <h1 class="brand-title">海泡订单系统</h1>
        <p class="brand-subtitle">专业的订单管理解决方案</p>
      </div>
      
      <!-- 气泡冒泡进度条 -->
      <div class="progress-container">
        <div class="bubble-progress">
          <div class="progress-track">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <!-- 动态生成的冒泡气泡 -->
            <div 
              v-for="bubble in activeBubbles" 
              :key="bubble.id"
              class="floating-bubble"
              :style="getBubbleStyle(bubble)"
            ></div>
          </div>
        </div>
        <div class="progress-text">{{ loadingText }} {{ Math.round(progress) }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useUserStore } from '../stores/user'

const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['finished'])

const userStore = useUserStore()
const progress = ref(0)
const loadingText = ref('加载系统资源')

// 动态气泡数据
const activeBubbles = ref([])
let bubbleId = 0

// 生成冒泡气泡
const createBubble = () => {
  const bubble = {
    id: bubbleId++,
    left: Math.random() * 90 + 5, // 5% - 95% 范围内随机位置
    size: Math.random() * 8 + 4,
    duration: Math.random() * 2 + 2, // 2-4秒动画时长
    delay: Math.random() * 0.5
  }
  
  activeBubbles.value.push(bubble)
  
  // 动画结束后移除气泡
  setTimeout(() => {
    const index = activeBubbles.value.findIndex(b => b.id === bubble.id)
    if (index > -1) {
      activeBubbles.value.splice(index, 1)
    }
  }, (bubble.duration + bubble.delay) * 1000)
}

// 气泡样式
const getBubbleStyle = (bubble) => {
  return {
    left: bubble.left + '%',
    width: bubble.size + 'px',
    height: bubble.size + 'px',
    animationDuration: bubble.duration + 's',
    animationDelay: bubble.delay + 's'
  }
}

// 加载步骤 - 只保留加载系统资源
const loadingSteps = [
  { text: '加载系统资源', duration: 2600, action: 'preloadResources' }
]

let currentStep = 0
let progressInterval = null
let bubbleInterval = null

const startLoading = async () => {
  if (!props.visible) return
  
  const totalDuration = loadingSteps.reduce((sum, step) => sum + step.duration, 0)
  let elapsed = 0
  
  // 开始生成气泡
  bubbleInterval = setInterval(() => {
    if (progress.value < 100) {
      createBubble()
    }
  }, 300) // 每300ms生成一个气泡
  
  progressInterval = setInterval(async () => {
    elapsed += 50
    const newProgress = Math.min((elapsed / totalDuration) * 100, 100)
    progress.value = newProgress
    
    // 检查是否需要执行步骤动作
    let stepElapsed = 0
    for (let i = 0; i < loadingSteps.length; i++) {
      const stepEndTime = stepElapsed + loadingSteps[i].duration
      
      if (elapsed > stepElapsed && elapsed <= stepEndTime) {
        if (currentStep !== i) {
          currentStep = i
          loadingText.value = loadingSteps[i].text
          
          if (loadingSteps[i].action) {
            await executeStepAction(loadingSteps[i].action)
          }
        }
        break
      }
      stepElapsed = stepEndTime
    }
    
    if (newProgress >= 100) {
      clearInterval(progressInterval)
      clearInterval(bubbleInterval)
      setTimeout(() => {
        emit('finished')
      }, 200)
    }
  }, 50)
}

const executeStepAction = async (action) => {
  try {
    switch (action) {
      case 'preloadResources':
        await preloadResources()
        break
    }
  } catch (error) {
    console.warn('加载步骤执行失败:', action, error)
  }
}

const preloadResources = () => {
  return new Promise((resolve) => {
    const resources = ['/haipao.svg']
    let loadedCount = 0
    
    if (resources.length === 0) {
      resolve()
      return
    }
    
    resources.forEach(src => {
      const img = new Image()
      img.onload = img.onerror = () => {
        loadedCount++
        if (loadedCount === resources.length) {
          resolve()
        }
      }
      img.src = src
    })
  })
}

onMounted(() => {
  if (props.visible) {
    setTimeout(startLoading, 800)
  }
})

watch(() => props.visible, (newVal) => {
  if (newVal) {
    progress.value = 0
    currentStep = 0
    loadingText.value = '加载系统资源'
    activeBubbles.value = []
    setTimeout(startLoading, 800)
  } else {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
    if (bubbleInterval) {
      clearInterval(bubbleInterval)
      bubbleInterval = null
    }
  }
})
</script>

<style scoped>
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, 
    #001122 0%,     /* 深海蓝 */
    #003366 25%,    /* 中海蓝 */
    #0066aa 50%,    /* 海蓝 */
    #0099cc 75%,    /* 浅海蓝 */
    #00ccff 100%    /* 海面蓝 */
  );
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  overflow: hidden;
}

.simple-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
}

.splash-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.logo-container {
  margin-bottom: 40px;
  position: relative;
}

.logo-wrapper {
  position: relative;
  display: inline-block;
}

.logo {
  width: 120px;
  height: 120px;
  filter: drop-shadow(0 0 20px rgba(0, 242, 254, 0.5));
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  background: radial-gradient(circle, rgba(0, 242, 254, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1); 
    opacity: 0.5; 
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.1); 
    opacity: 0.8; 
  }
}

.brand-container {
  margin-bottom: 60px;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 10px 0;
  letter-spacing: 2px;
  text-shadow: 0 0 20px rgba(0, 242, 254, 0.5);
  animation: fadeInUp 1s ease-out 0.5s both;
}

.brand-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  animation: fadeInUp 1s ease-out 0.7s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-container {
  width: 400px;
  margin: 0 auto;
  animation: fadeInUp 1s ease-out 0.9s both;
}

.bubble-progress {
  position: relative;
  width: 100%;
  height: 60px;
  margin-bottom: 25px;
}

.progress-track {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 8px;
  background: rgba(0, 50, 100, 0.3);
  border: 1px solid rgba(0, 242, 254, 0.3);
  border-radius: 10px;
  overflow: visible;
  box-shadow: 0 0 15px rgba(0, 242, 254, 0.2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(0, 242, 254, 0.8) 0%, 
    rgba(79, 172, 254, 0.9) 50%,
    rgba(0, 242, 254, 1) 100%);
  border-radius: 10px;
  transition: width 0.5s ease;
  box-shadow: 0 0 10px rgba(0, 242, 254, 0.5);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 100%);
  border-radius: 0 10px 10px 0;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.floating-bubble {
  position: absolute;
  bottom: 8px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, 
    rgba(255, 255, 255, 0.8) 0%, 
    rgba(0, 242, 254, 0.6) 40%, 
    rgba(79, 172, 254, 0.4) 100%);
  box-shadow: 
    0 0 15px rgba(0, 242, 254, 0.6),
    inset 2px 2px 4px rgba(255, 255, 255, 0.3);
  animation: bubbleRise linear forwards;
  pointer-events: none;
}

.floating-bubble::before {
  content: '';
  position: absolute;
  top: 15%;
  left: 25%;
  width: 25%;
  height: 25%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: highlight 1s ease-in-out infinite alternate;
}

@keyframes bubbleRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-25px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(-50px) scale(0.8);
    opacity: 0;
  }
}

@keyframes highlight {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

.progress-text {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 300;
  margin-top: 10px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .brand-title {
    font-size: 2rem;
  }
  
  .brand-subtitle {
    font-size: 1rem;
  }
  
  .progress-container {
    width: 300px;
  }
  
  .logo {
    width: 100px;
    height: 100px;
  }
  
  .logo-glow {
    width: 120px;
    height: 120px;
  }
  
  .bubble-progress {
    height: 50px;
  }
  
  .progress-track {
    height: 6px;
  }
  
  .floating-bubble {
    bottom: 6px;
  }
}
</style>

