<template>
  <div class="income-statistics">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="hover">
        <template #header>
          <div class="filter-header">
            <div class="filter-title">
              <el-icon class="filter-icon"><Filter /></el-icon>
              <span>数据筛选</span>
              <el-tag v-if="filterCollapsed" type="info" size="small" class="filter-summary">
                {{ getFilterSummary() }}
              </el-tag>
            </div>
            <div class="filter-header-actions">
              <div class="filter-actions" v-if="!filterCollapsed">
                <el-button type="primary" @click="loadStatistics" :loading="loading" size="small">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
                <el-button @click="resetFilters" size="small">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </div>
              <el-button
                @click="toggleFilterCollapse"
                size="small"
                text
                class="collapse-btn"
                :title="filterCollapsed ? '展开筛选' : '折叠筛选'"
              >
                <el-icon class="collapse-icon" :class="{ 'collapsed': filterCollapsed }">
                  <ArrowDown />
                </el-icon>
              </el-button>
            </div>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="!filterCollapsed" class="filter-content">
            <div class="filter-row">
              <div class="filter-group">
                <div class="filter-item">
                  <label class="filter-label">
                    <el-icon><Calendar /></el-icon>
                    时间维度
                  </label>
                  <el-select v-model="queryForm.timeDimension" @change="handleTimeDimensionChange" class="filter-select">
                    <el-option label="按年统计" value="year">
                      <el-icon><Calendar /></el-icon>
                      <span style="margin-left: 8px">按年统计</span>
                    </el-option>
                    <el-option label="按月统计" value="month">
                      <el-icon><Calendar /></el-icon>
                      <span style="margin-left: 8px">按月统计</span>
                    </el-option>
                    <el-option label="按周统计" value="week">
                      <el-icon><Calendar /></el-icon>
                      <span style="margin-left: 8px">按周统计</span>
                    </el-option>
                    <el-option label="按日统计" value="day">
                      <el-icon><Calendar /></el-icon>
                      <span style="margin-left: 8px">按日统计</span>
                    </el-option>
                  </el-select>
                </div>

                <div class="filter-item">
                  <label class="filter-label">
                    <el-icon><Clock /></el-icon>
                    时间范围
                  </label>
                  <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="handleDateRangeChange"
                    class="filter-date-picker"
                  />
                </div>
              </div>

              <div class="filter-group">
                <div class="filter-item" v-if="userStore.userInfo.role === 0">
                  <label class="filter-label">
                    <el-icon><User /></el-icon>
                    用户筛选
                  </label>
                  <el-select v-model="queryForm.userId" clearable placeholder="选择用户" class="filter-select">
                    <el-option label="全部用户" value="">
                      <el-icon><UserFilled /></el-icon>
                      <span style="margin-left: 8px">全部用户</span>
                    </el-option>
                    <el-option
                      v-for="user in userList"
                      :key="user.id"
                      :label="user.realName"
                      :value="user.id"
                    >
                      <el-icon><Avatar /></el-icon>
                      <span style="margin-left: 8px">{{ user.realName }}</span>
                    </el-option>
                  </el-select>
                </div>

                <div class="filter-item">
                  <label class="filter-label">
                    <el-icon><DocumentChecked /></el-icon>
                    统计范围
                  </label>
                  <el-select v-model="queryForm.onlySettled" class="filter-select">
                    <el-option label="已完成+已结算" :value="false">
                      <el-icon><CircleCheck /></el-icon>
                      <span style="margin-left: 8px">已完成+已结算</span>
                    </el-option>
                    <el-option label="仅已结算" :value="true">
                      <el-icon><Select /></el-icon>
                      <span style="margin-left: 8px">仅已结算</span>
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 快速时间选择 -->
            <div class="quick-filters">
              <div class="quick-label">
                <el-icon><Timer /></el-icon>
                <span>快速选择</span>
              </div>
              <div class="quick-buttons">
                <el-button
                  v-for="item in quickTimeOptions"
                  :key="item.value"
                  size="small"
                  :type="item.active ? 'primary' : ''"
                  @click="setQuickRange(item.value)"
                  class="quick-btn"
                >
                  <el-icon><component :is="item.icon" /></el-icon>
                  {{ item.label }}
                </el-button>
              </div>
            </div>

            <!-- 折叠状态下的操作按钮 -->
            <div class="collapsed-actions">
              <el-button type="primary" @click="loadStatistics" :loading="loading" size="small">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="resetFilters" size="small">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </div>
        </el-collapse-transition>

        <!-- 折叠状态下的快速操作 -->
        <div v-if="filterCollapsed" class="collapsed-quick-actions">
          <div class="quick-buttons-collapsed">
            <el-button
              v-for="item in quickTimeOptions"
              :key="item.value"
              size="small"
              :type="item.active ? 'primary' : ''"
              @click="setQuickRange(item.value)"
              class="quick-btn-collapsed"
            >
              {{ item.label }}
            </el-button>
          </div>
          <div class="collapsed-main-actions">
            <el-button type="primary" @click="loadStatistics" :loading="loading" size="small">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetFilters" size="small">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-overview" v-if="statisticsData.overall">
      <div class="overview-header">
        <h3 class="overview-title">
          <el-icon><DataAnalysis /></el-icon>
          数据概览
        </h3>
        <div class="overview-period">
          <el-tag type="info" size="small">
            <el-icon><Clock /></el-icon>
            统计周期：{{ formatPeriod() }}
          </el-tag>
        </div>
      </div>

      <div class="statistics-cards">
        <div class="stat-card primary-card">
          <div class="card-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatNumber(statisticsData.overall.totalAmount) }}</div>
            <div class="card-title">接单总金额</div>
            <div class="card-subtitle">所有任务的总金额</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-icon up"><ArrowUp /></el-icon>
            <span class="trend-text">+12.5%</span>
          </div>
        </div>

        <div class="stat-card success-card">
          <div class="card-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatNumber(statisticsData.overall.totalNetIncome) }}</div>
            <div class="card-title">实得收入</div>
            <div class="card-subtitle">已完成/已结算收入</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-icon up"><ArrowUp /></el-icon>
            <span class="trend-text">+8.3%</span>
          </div>
        </div>

        <div class="stat-card warning-card">
          <div class="card-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatNumber(statisticsData.overall.totalDispatcherFee) }}</div>
            <div class="card-title">派单员抽成</div>
            <div class="card-subtitle">派单员获得的抽成</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-icon up"><ArrowUp /></el-icon>
            <span class="trend-text">+5.7%</span>
          </div>
        </div>

        <div class="stat-card info-card">
          <div class="card-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statisticsData.overall.totalTaskCount }}</div>
            <div class="card-title">任务总数</div>
            <div class="card-subtitle">统计期间内的任务数量</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-icon up"><ArrowUp /></el-icon>
            <span class="trend-text">+15.2%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 收入趋势图 -->
        <el-col :span="24" :lg="16">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <span>收入趋势分析</span>
                <el-radio-group v-model="chartType" size="small">
                  <el-radio-button label="line">折线图</el-radio-button>
                  <el-radio-button label="bar">柱状图</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <income-chart
              :type="chartType"
              :data="chartData"
              :title="''"
              height="400px"
              @chart-click="handleChartClick"
            />
          </el-card>
        </el-col>
        
        <!-- 用户排行榜 -->
        <el-col :span="24" :lg="8">
          <el-card class="ranking-card" shadow="never">
            <template #header>
              <div class="ranking-header">
                <div class="ranking-title">
                  <el-icon class="ranking-icon"><Trophy /></el-icon>
                  <span>用户排行榜</span>
                </div>
                <div class="ranking-actions">
                  <el-select v-model="rankingType" size="small" @change="loadUserRanking" class="ranking-select" style="margin-right: 8px; width: 100px;">
                    <el-option label="仅用户" value="user" />
                    <el-option label="混合排名" value="mixed" />
                  </el-select>
                  <el-select v-model="rankingSort" size="small" @change="loadUserRanking" class="ranking-select">
                    <el-option label="按总金额" value="totalAmount">
                      <el-icon><Money /></el-icon>
                      <span style="margin-left: 8px">按总金额</span>
                    </el-option>
                    <el-option label="按实得收入" value="netIncome">
                      <el-icon><Wallet /></el-icon>
                      <span style="margin-left: 8px">按实得收入</span>
                    </el-option>
                    <el-option label="按任务数量" value="taskCount">
                      <el-icon><Document /></el-icon>
                      <span style="margin-left: 8px">按任务数量</span>
                    </el-option>
                  </el-select>
                </div>
              </div>
            </template>

            <div class="ranking-container" v-loading="rankingLoading">
              <!-- 简化的排行榜列表 -->
              <div v-if="userRanking.length > 0" class="ranking-list-simple">
                <div
                  v-for="(user, index) in userRanking"
                  :key="user.userId"
                  class="ranking-item-simple"
                  :class="{ 'top-three': index < 3 }"
                >
                  <div class="rank-number" :class="`rank-${index + 1}`">
                    {{ index + 1 }}
                  </div>
                  <el-avatar :size="32" class="user-avatar">
                    {{ user.realName?.charAt(0) || 'U' }}
                  </el-avatar>
                  <div class="user-info">
                    <div class="user-name">
                      {{ user.realName }}
                      <el-tag v-if="user.role === 0" size="small" type="warning" style="margin-left: 4px;">管理员</el-tag>
                    </div>
                    <div class="user-value">
                      <span v-if="rankingSort === 'totalAmount'">
                        ¥{{ formatNumber(user.totalAmount) }}
                      </span>
                      <span v-else-if="rankingSort === 'netIncome'">
                        ¥{{ formatNumber(user.netIncome) }}
                      </span>
                      <span v-else>
                        {{ user.taskCount }} 个任务
                      </span>
                    </div>
                  </div>
                  <div class="task-count">{{ user.taskCount }} 个</div>
                </div>
              </div>

              <!-- 空数据状态 -->
              <div v-if="userRanking.length === 0" class="no-data-ranking">
                <el-icon class="no-data-icon"><User /></el-icon>
                <div class="no-data-text">暂无排行数据</div>
                <div class="no-data-desc">请调整筛选条件后重试</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势数据详情 -->
    <el-card class="table-card-simple" shadow="never">
      <template #header>
        <div class="table-header-simple">
          <span class="table-title-simple">趋势数据详情</span>
          <el-button size="small" @click="exportTrendData" :icon="Download">
            导出数据
          </el-button>
        </div>
      </template>

      <!-- 简化表格 -->
      <el-table
        :data="statisticsData.trendData"
        v-loading="loading"
        class="trend-table-simple"
        stripe
      >
        <el-table-column prop="timeLabel" label="时间" min-width="100">
          <template #default="{ row }">
            <span>{{ row.timeLabel }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="totalAmount" label="接单总金额" min-width="120" sortable>
          <template #default="{ row }">
            <span class="amount-text">¥{{ formatNumber(row.totalAmount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="netIncome" label="实际收入" min-width="120" sortable>
          <template #default="{ row }">
            <span class="income-text">¥{{ formatNumber(row.netIncome) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="commissionAmount" label="抽成费用" min-width="120" sortable>
          <template #default="{ row }">
            <span class="commission-text">¥{{ formatNumber(calculateCommission(row)) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="taskCount" label="任务数量" min-width="100" sortable>
          <template #default="{ row }">
            <span class="task-text">{{ row.taskCount || 0 }} 个</span>
          </template>
        </el-table-column>

        <el-table-column label="完成率" min-width="100">
          <template #default="{ row }">
            <el-progress
              :percentage="calculateCompletionRate(row)"
              :show-text="true"
              :stroke-width="6"
              :color="getProgressColor(calculateCompletionRate(row))"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="80" fixed="right">
          <template #default="{ row }">
            <el-button size="small" text @click="viewDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空数据状态 -->
      <div v-if="!statisticsData.trendData || statisticsData.trendData.length === 0" class="no-trend-data">
        <el-icon class="no-data-icon"><DataLine /></el-icon>
        <div class="no-data-text">暂无趋势数据</div>
        <div class="no-data-desc">请调整筛选条件后重试</div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Refresh,
  Filter,
  Calendar,
  User,
  TrendCharts,
  DataLine,
  Download,
  Trophy,
  Money,
  Wallet,
  Document
} from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'
import { statisticsApi, StatisticsQueryBuilder } from '../../api/statistics'
import { userApi } from '../../api/user'
import { statisticsManager } from '../../utils/statisticsManager'
import { processChartData, validateChartData } from '../../utils/chartDataProcessor'
import IncomeChart from '../../components/charts/IncomeChart.vue'
import StatisticsCard from '../../components/statistics/StatisticsCard.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const rankingLoading = ref(false)
const chartType = ref('line')
const rankingSort = ref('totalAmount')
const rankingType = ref('user') // 'user' 仅用户, 'mixed' 混合排名
const dateRange = ref([])
const userList = ref([])

// 查询表单
const queryForm = reactive({
  timeDimension: 'month',
  startTime: null,
  endTime: null,
  userId: '',
  onlySettled: false
})

// 统计数据
const statisticsData = ref({
  overall: null,
  trendData: [],
  userRanking: [],
  chartData: null
})

// 用户排行数据
const userRanking = ref([])

// 快速时间选择选项
const quickTimeOptions = ref([
  { label: '最近一周', value: 'week', icon: 'Calendar', active: false },
  { label: '最近一月', value: 'month', icon: 'Calendar', active: false },
  { label: '最近三月', value: 'quarter', icon: 'Calendar', active: false },
  { label: '本年度', value: 'year', icon: 'Calendar', active: false }
])

// 筛选卡片折叠状态
const filterCollapsed = ref(false)

// 移除了表格视图切换功能

// 图表数据
const chartData = computed(() => {
  if (!statisticsData.value || !statisticsData.value.trendData) {
    return {
      xAxisLabels: [],
      totalAmountSeries: [],
      netIncomeSeries: [],
      dispatcherFeeSeries: [],
      taskCountSeries: []
    }
  }

  // 使用数据处理工具处理图表数据
  const processedData = processChartData(statisticsData.value)

  // 验证数据
  const validation = validateChartData(processedData)
  if (!validation.isValid) {
    console.warn('图表数据验证失败:', validation.errors)
  }

  return processedData
})

// 页面初始化
onMounted(() => {
  initPage()
})

async function initPage() {
  // 恢复筛选卡片折叠状态
  const savedCollapsed = localStorage.getItem('income-statistics-filter-collapsed')
  if (savedCollapsed !== null) {
    filterCollapsed.value = savedCollapsed === 'true'
  }

  // 设置默认时间范围（最近3个月）
  setQuickRange('quarter')

  // 加载用户列表（管理员可见）
  if (userStore.userInfo.role === 0) {
    await loadUserList()
  }

  // 加载统计数据
  await loadStatistics()
}

// 加载用户列表
async function loadUserList() {
  try {
    const response = await userApi.getUserList({ role: 1, status: 1 })
    userList.value = response.data.records || []
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

// 加载统计数据
async function loadStatistics() {
  try {
    const query = StatisticsQueryBuilder.create()
    StatisticsQueryBuilder.setTimeDimension(query, queryForm.timeDimension)
    StatisticsQueryBuilder.setTimeRange(query, queryForm.startTime, queryForm.endTime)

    if (queryForm.userId && queryForm.userId !== '') {
      StatisticsQueryBuilder.setUser(query, queryForm.userId)
    }

    StatisticsQueryBuilder.setOnlySettled(query, queryForm.onlySettled)

    // 使用性能优化的管理器
    loading.value = statisticsManager.getLoadingState('income')

    const response = await statisticsManager.getIncomeStatistics(query, {
      useCache: true,
      debounce: true,
      loadingKey: 'income'
    })

    statisticsData.value = response.data

    // 同时加载用户排行
    await loadUserRanking()
  } catch (error) {
    ElMessage.error('加载统计数据失败: ' + (error.message || '未知错误'))
    console.error('统计数据加载错误:', error)
  } finally {
    loading.value = false
  }
}

// 加载用户排行
async function loadUserRanking() {
  if (userStore.userInfo.role !== 0) return

  try {
    const query = StatisticsQueryBuilder.create()
    StatisticsQueryBuilder.setTimeDimension(query, queryForm.timeDimension)
    StatisticsQueryBuilder.setTimeRange(query, queryForm.startTime, queryForm.endTime)
    StatisticsQueryBuilder.setSort(query, rankingSort.value, 'desc')
    StatisticsQueryBuilder.setLimit(query, 10)
    StatisticsQueryBuilder.setOnlySettled(query, queryForm.onlySettled)

    // 设置排名类型参数
    if (rankingType.value === 'mixed') {
      StatisticsQueryBuilder.setIncludeAdmin(query, true)
    } else {
      StatisticsQueryBuilder.setIncludeAdmin(query, false)
    }

    rankingLoading.value = statisticsManager.getLoadingState('ranking')

    const response = await statisticsManager.getUserRanking(query, {
      useCache: true,
      loadingKey: 'ranking'
    })

    userRanking.value = response.data.userRanking || []
  } catch (error) {
    console.error('加载用户排行失败:', error)
    ElMessage.error('加载用户排行失败')
  } finally {
    rankingLoading.value = false
  }
}

// 时间维度变化处理
function handleTimeDimensionChange() {
  loadStatistics()
}

// 日期范围变化处理
function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    queryForm.startTime = value[0]
    queryForm.endTime = value[1]
  } else {
    queryForm.startTime = null
    queryForm.endTime = null
  }
}

// 快速时间范围设置
function setQuickRange(type) {
  const now = new Date()
  let startTime, endTime

  switch (type) {
    case 'week':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'month':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'quarter':
      startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'year':
      startTime = new Date(now.getFullYear(), 0, 1)
      endTime = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
      break
  }

  queryForm.startTime = formatDateTime(startTime)
  queryForm.endTime = formatDateTime(endTime)
  dateRange.value = [queryForm.startTime, queryForm.endTime]
}

// 重置筛选条件
function resetFilters() {
  queryForm.timeDimension = 'month'
  queryForm.userId = null
  queryForm.onlySettled = false
  setQuickRange('quarter')
  loadStatistics()
}

// 图表点击事件
function handleChartClick(params) {
  console.log('图表点击:', params)
  // 可以在这里实现点击图表跳转到详细页面的逻辑
}

// 格式化日期时间
function formatDateTime(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化数字
function formatNumber(num) {
  if (!num) return '0'
  return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 })
}

// 格式化统计周期
function formatPeriod() {
  if (!queryForm.startTime || !queryForm.endTime) return '全部时间'
  const start = new Date(queryForm.startTime).toLocaleDateString('zh-CN')
  const end = new Date(queryForm.endTime).toLocaleDateString('zh-CN')
  return `${start} - ${end}`
}

// 切换筛选卡片折叠状态
function toggleFilterCollapse() {
  filterCollapsed.value = !filterCollapsed.value

  // 保存折叠状态到本地存储
  localStorage.setItem('income-statistics-filter-collapsed', filterCollapsed.value.toString())
}

// 获取筛选条件摘要
function getFilterSummary() {
  const summary = []

  // 时间维度
  const timeDimensionMap = {
    year: '按年',
    month: '按月',
    week: '按周',
    day: '按日'
  }
  summary.push(timeDimensionMap[queryForm.timeDimension] || '按月')

  // 时间范围
  if (queryForm.startTime && queryForm.endTime) {
    const start = new Date(queryForm.startTime).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    const end = new Date(queryForm.endTime).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    summary.push(`${start} - ${end}`)
  }

  // 用户筛选
  if (queryForm.userId && userList.value.length > 0) {
    const user = userList.value.find(u => u.id === queryForm.userId)
    if (user) {
      summary.push(user.realName)
    }
  }

  // 统计范围
  summary.push(queryForm.onlySettled ? '仅已结算' : '已完成+已结算')

  return summary.join(' | ')
}

// 计算排行榜进度条百分比
function calculateProgress(user) {
  if (!userRanking.value || userRanking.value.length === 0) return 0

  const topUser = userRanking.value[0]
  let maxValue = 0
  let currentValue = 0

  if (rankingSort.value === 'totalAmount') {
    maxValue = topUser.totalAmount || 0
    currentValue = user.totalAmount || 0
  } else if (rankingSort.value === 'netIncome') {
    maxValue = topUser.netIncome || 0
    currentValue = user.netIncome || 0
  } else {
    maxValue = topUser.taskCount || 0
    currentValue = user.taskCount || 0
  }

  if (maxValue === 0) return 0
  return Math.round((currentValue / maxValue) * 100)
}

// 获取表格行样式
function getRowClassName({ rowIndex }) {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取进度条颜色
function getProgressColor(percentage) {
  if (percentage >= 80) return '#67C23A'
  if (percentage >= 60) return '#E6A23C'
  if (percentage >= 40) return '#409EFF'
  return '#F56C6C'
}

// 处理表格行点击
function handleRowClick(row) {
  console.log('点击行:', row)
  // 可以在这里实现行点击的详细逻辑
}

// 查看详情
function viewDetails(row) {
  console.log('查看详情:', row)
  // 可以在这里实现查看详情的逻辑
}

// 导出趋势数据
function exportTrendData() {
  console.log('导出趋势数据')
  // 可以在这里实现导出功能
}

// 计算抽成费用
function calculateCommission(row) {
  if (!row.totalAmount || !row.netIncome) return 0
  return row.totalAmount - row.netIncome
}

// 计算完成率
function calculateCompletionRate(row) {
  if (!row.taskCount || row.taskCount === 0) return 0
  const completedCount = row.completedCount || 0
  return Math.round((completedCount / row.taskCount) * 100)
}
</script>

<style scoped>
.income-statistics {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 60px);
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.filter-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.filter-icon {
  color: #409EFF;
}

.filter-summary {
  margin-left: 12px;
  font-weight: normal;
  font-size: 12px;
}

.filter-header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collapse-btn {
  padding: 4px 8px;
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  background-color: #f0f0f0;
  border-radius: 6px;
}

.collapse-icon {
  transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

.filter-content {
  margin-top: 16px;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-group {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.filter-select,
.filter-date-picker {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.filter-select:hover,
.filter-date-picker:hover {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.quick-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
}

.quick-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.quick-buttons {
  display: flex;
  gap: 8px;
}

.quick-btn {
  border-radius: 20px;
  padding: 6px 16px;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 折叠状态样式 */
.collapsed-quick-actions {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.quick-buttons-collapsed {
  display: flex;
  gap: 8px;
  flex: 1;
}

.quick-btn-collapsed {
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.quick-btn-collapsed:hover {
  background: #409EFF;
  color: white;
  border-color: #409EFF;
  transform: translateY(-1px);
}

.collapsed-main-actions {
  display: flex;
  gap: 8px;
}

.collapsed-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

/* 折叠动画优化 */
.el-collapse-transition {
  transition: all 0.3s ease;
}

/* 筛选卡片折叠状态优化 */
.filter-card.collapsed {
  background: rgba(255, 255, 255, 0.98);
}

.filter-card.collapsed .filter-header {
  padding-bottom: 0;
}

/* 统计概览样式 */
.statistics-overview {
  margin-bottom: 24px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.overview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.overview-period {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  position: relative;
  padding: 24px;
  border-radius: 16px;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.primary-card {
  --card-color: #409EFF;
  --card-color-light: #79bbff;
}

.success-card {
  --card-color: #67C23A;
  --card-color-light: #95d475;
}

.warning-card {
  --card-color: #E6A23C;
  --card-color-light: #ebb563;
}

.info-card {
  --card-color: #909399;
  --card-color-light: #b1b3b8;
}

.card-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.card-content {
  margin-right: 60px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 4px;
}

.card-subtitle {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.card-trend {
  position: absolute;
  bottom: 16px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.trend-icon {
  font-size: 14px;
}

.trend-icon.up {
  color: #67C23A;
}

.trend-icon.down {
  color: #F56C6C;
}

.trend-text {
  color: #67C23A;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card,
.ranking-card,
.table-card {
  border-radius: 8px;
  border: none;
}

.chart-header,
.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}


.ranking-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 12px;
  background-color: #f0f0f0;
  color: #606266;
}


.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}


/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .income-statistics {
    padding: 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filter-actions {
    margin-left: 0;
  }

  .quick-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .statistics-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-header,
  .ranking-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

/* 卡片阴影效果 */
.filter-card,
.chart-card,
.ranking-card
{
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s ease;
}

.filter-card:hover,
.chart-card:hover,
.ranking-card:hover{
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 排行榜样式 */
.ranking-card {
  height: 100%;
  overflow: hidden;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.ranking-icon {
  color: #FFD700;
  font-size: 18px;
}

.ranking-select {
  min-width: 120px;
}

/* 简化的用户排行榜样式 */
.ranking-container {
  padding: 16px;
}

.ranking-list-simple {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.ranking-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ranking-item-simple {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.2s ease;
  cursor: pointer;
}

.ranking-item-simple:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.ranking-item-simple.top-three {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #409eff;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f5f7fa;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.rank-number.rank-1 {
  background: #ffd700;
  color: white;
}

.rank-number.rank-2 {
  background: #c0c0c0;
  color: white;
}

.rank-number.rank-3 {
  background: #cd7f32;
  color: white;
}

.user-avatar {
  margin-right: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: bold;
}

.user-info {
  flex: 1;
  margin-right: 12px;
}

.user-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
  font-size: 14px;
}

.user-value {
  font-size: 13px;
  color: #409eff;
  font-weight: 500;
}

.task-count {
  font-size: 12px;
  color: #909399;
}

/* 简化的表格样式 */
.table-card-simple {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.table-title-simple {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.trend-table-simple {
  width: 100%;
}

.trend-table-simple .el-table__header-wrapper {
  background: #f8f9fa;
}

.trend-table-simple .el-table__header th {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.trend-table-simple .el-table__row:hover {
  background: #f0f9ff;
}

.amount-text {
  color: #409eff;
  font-weight: 500;
}

.income-text {
  color: #67c23a;
  font-weight: 500;
}

.commission-text {
  color: #e6a23c;
  font-weight: 500;
}

.task-text {
  color: #606266;
}


.user-info {
  flex: 1;
  margin-right: 12px;
}

.user-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
}


/* 空数据样式 */
.no-data-ranking {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.no-data-icon {
  font-size: 48px;
  color: #dcdfe6;
  margin-bottom: 16px;
}

.no-data-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.no-data-desc {
  font-size: 14px;
  color: #909399;
}

.amount-cell.success .amount-icon {
  color: #67C23A;
}

.amount-cell.warning .amount-icon {
  color: #E6A23C;
}


/* 表格行样式 */
:deep(.trend-table .even-row) {
  background-color: #fafafa;
}

:deep(.trend-table .odd-row) {
  background-color: white;
}

:deep(.trend-table .el-table__row:hover) {
  background-color: #f0f9ff !important;
}

.metric-item.primary .metric-icon {
  background: #409EFF;
  color: white;
}

.metric-item.success .metric-icon {
  background: #67C23A;
  color: white;
}

.metric-item.warning .metric-icon {
  background: #E6A23C;
  color: white;
}

.metric-item.info .metric-icon {
  background: #909399;
  color: white;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  word-break: break-all;
}

/* 空数据状态 */
.no-trend-data {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
}

.no-trend-data .no-data-icon {
  font-size: 64px;
  color: #dcdfe6;
  margin-bottom: 20px;
}

.no-trend-data .no-data-text {
  font-size: 18px;
  color: #606266;
  margin-bottom: 8px;
}

.no-trend-data .no-data-desc {
  font-size: 14px;
  color: #909399;
}
</style>
