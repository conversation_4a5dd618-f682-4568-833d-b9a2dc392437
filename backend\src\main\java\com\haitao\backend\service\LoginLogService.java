package com.haitao.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haitao.backend.dto.LoginLogResponse;
import com.haitao.backend.dto.LogQueryRequest;

/**
 * 登录日志服务接口
 */
public interface LoginLogService {
    
    /**
     * 记录登录日志
     */
    void saveLoginLog(Long userId, String ipAddress, String userAgent, Integer loginResult);
    
    /**
     * 分页查询登录日志
     */
    IPage<LoginLogResponse> getLoginLogList(LogQueryRequest request);
}
