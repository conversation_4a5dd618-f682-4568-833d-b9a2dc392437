<template>
  <div class="my-income-statistics">
    <!-- 个人信息卡片 -->
    <div class="user-profile-card">
      <el-card class="profile-card" shadow="hover">
        <div class="profile-content">
          <div class="profile-avatar">
            <el-avatar :size="80" class="user-avatar">
              {{ userStore.userInfo.realName?.charAt(0) || 'U' }}
            </el-avatar>
            <div class="online-badge"></div>
          </div>
          <div class="profile-info">
            <h2 class="user-name">{{ userStore.userInfo.realName || userStore.userInfo.username }}</h2>
            <p class="user-role">{{ userStore.userInfo.role === 0 ? '系统管理员' : '普通成员' }}</p>
            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-label">用户名</span>
                <span class="stat-value">{{ userStore.userInfo.username }}</span>
              </div>
              <div class="stat-item" v-if="userStore.userInfo.phone">
                <span class="stat-label">联系方式</span>
                <span class="stat-value">{{ userStore.userInfo.phone }}</span>
              </div>
            </div>
          </div>
          <div class="profile-actions">
            <el-button type="primary" @click="loadStatistics" :loading="loading" size="small">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="hover">
        <template #header>
          <div class="filter-header">
            <div class="filter-title">
              <el-icon class="filter-icon"><Filter /></el-icon>
              <span>筛选条件</span>
              <el-tag v-if="filterCollapsed" type="info" size="small" class="filter-summary">
                {{ getFilterSummary() }}
              </el-tag>
            </div>
            <el-button
              @click="toggleFilterCollapse"
              size="small"
              text
              class="collapse-btn"
              :title="filterCollapsed ? '展开筛选' : '折叠筛选'"
            >
              <el-icon class="collapse-icon" :class="{ 'collapsed': filterCollapsed }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="!filterCollapsed" class="filter-content">
            <div class="filter-row">
              <div class="filter-group">
                <div class="filter-item">
                  <label class="filter-label">
                    <el-icon><Calendar /></el-icon>
                    时间维度
                  </label>
                  <el-select v-model="queryForm.timeDimension" @change="handleTimeDimensionChange" class="filter-select">
                    <el-option label="按年统计" value="year" />
                    <el-option label="按月统计" value="month" />
                    <el-option label="按周统计" value="week" />
                    <el-option label="按日统计" value="day" />
                  </el-select>
                </div>

                <div class="filter-item">
                  <label class="filter-label">
                    <el-icon><Clock /></el-icon>
                    时间范围
                  </label>
                  <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="handleDateRangeChange"
                    class="filter-date-picker"
                  />
                </div>

                <div class="filter-item">
                  <label class="filter-label">
                    <el-icon><DocumentChecked /></el-icon>
                    统计范围
                  </label>
                  <el-select v-model="queryForm.onlySettled" class="filter-select">
                    <el-option label="已完成+已结算" :value="false" />
                    <el-option label="仅已结算" :value="true" />
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 快速时间选择 -->
            <div class="quick-filters">
              <div class="quick-label">
                <el-icon><Timer /></el-icon>
                <span>快速选择</span>
              </div>
              <div class="quick-buttons">
                <el-button size="small" @click="setQuickRange('week')" class="quick-btn">
                  <el-icon><Calendar /></el-icon>
                  最近一周
                </el-button>
                <el-button size="small" @click="setQuickRange('month')" class="quick-btn">
                  <el-icon><Calendar /></el-icon>
                  最近一月
                </el-button>
                <el-button size="small" @click="setQuickRange('quarter')" class="quick-btn">
                  <el-icon><Calendar /></el-icon>
                  最近三月
                </el-button>
                <el-button size="small" @click="setQuickRange('year')" class="quick-btn">
                  <el-icon><Calendar /></el-icon>
                  本年度
                </el-button>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="filter-actions-bottom">
              <el-button type="primary" @click="loadStatistics" :loading="loading" size="small">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="resetFilters" size="small">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </div>
        </el-collapse-transition>

        <!-- 折叠状态下的快速操作 -->
        <div v-if="filterCollapsed" class="collapsed-quick-actions">
          <div class="quick-buttons-collapsed">
            <el-button size="small" @click="setQuickRange('week')" class="quick-btn-collapsed">
              最近一周
            </el-button>
            <el-button size="small" @click="setQuickRange('month')" class="quick-btn-collapsed">
              最近一月
            </el-button>
            <el-button size="small" @click="setQuickRange('quarter')" class="quick-btn-collapsed">
              最近三月
            </el-button>
            <el-button size="small" @click="setQuickRange('year')" class="quick-btn-collapsed">
              本年度
            </el-button>
          </div>
          <div class="collapsed-main-actions">
            <el-button type="primary" @click="loadStatistics" :loading="loading" size="small">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetFilters" size="small">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 个人统计卡片 -->
    <div class="personal-stats" v-if="statisticsData.overall">
      <div class="stats-header">
        <h3 class="stats-title">
          <el-icon><DataAnalysis /></el-icon>
          我的收入概览
        </h3>
        <div class="stats-period">
          <el-tag type="info" size="small" effect="light">
            <el-icon><Clock /></el-icon>
            统计周期：{{ formatPeriod() }}
          </el-tag>
        </div>
      </div>

      <div class="personal-cards">
        <div class="personal-card primary-gradient">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-trend">
              <el-icon class="trend-up"><ArrowUp /></el-icon>
              <span>+12.5%</span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-value">¥{{ formatNumber(statisticsData.overall.totalAmount) }}</div>
            <div class="card-title">接单总金额</div>
            <div class="card-desc">我的任务总金额</div>
          </div>
        </div>

        <div class="personal-card success-gradient">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-trend">
              <el-icon class="trend-up"><ArrowUp /></el-icon>
              <span>+8.3%</span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-value">¥{{ formatNumber(statisticsData.overall.totalNetIncome) }}</div>
            <div class="card-title">实得收入</div>
            <div class="card-desc">已完成/已结算收入</div>
          </div>
        </div>

        <div class="personal-card warning-gradient">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-trend">
              <el-icon class="trend-up"><ArrowUp /></el-icon>
              <span>+15.2%</span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-value">{{ statisticsData.overall.totalTaskCount }}</div>
            <div class="card-title">任务总数</div>
            <div class="card-desc">接收的任务数量</div>
          </div>
        </div>

        <div class="personal-card info-gradient">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><PieChart /></el-icon>
            </div>
            <div class="card-trend">
              <el-icon class="trend-up"><ArrowUp /></el-icon>
              <span>+5.7%</span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-value">{{ completionRate }}%</div>
            <div class="card-title">完成率</div>
            <div class="card-desc">任务完成情况</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 收入趋势图 -->
        <el-col :span="24" :lg="16">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <span>我的收入趋势</span>
                <el-radio-group v-model="chartType" size="small">
                  <el-radio-button label="line">折线图</el-radio-button>
                  <el-radio-button label="bar">柱状图</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <income-chart
              :type="chartType"
              :data="chartData"
              :title="''"
              height="400px"
            />
          </el-card>
        </el-col>
        
        <!-- 任务状态分布 -->
        <el-col :span="24" :lg="8">
          <el-card class="status-card" shadow="never">
            <template #header>
              <span>任务状态分布</span>
            </template>
            <div class="status-list" v-if="taskStatusData.length > 0">
              <div 
                v-for="status in taskStatusData" 
                :key="status.name"
                class="status-item"
              >
                <div class="status-info">
                  <div class="status-name">{{ status.name }}</div>
                  <div class="status-count">{{ status.count }} 个</div>
                </div>
                <div class="status-progress">
                  <el-progress 
                    :percentage="status.percentage" 
                    :color="status.color"
                    :show-text="false"
                  />
                </div>
              </div>
            </div>
            <div v-else class="no-data">
              暂无任务数据
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务详情表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <el-icon class="table-icon"><DataLine /></el-icon>
            <span>我的任务详情</span>
          </div>
          <div class="table-actions">
            <el-button-group size="small">
              <el-button
                :type="tableView === 'table' ? 'primary' : ''"
                @click="tableView = 'table'"
                :icon="Grid"
              >
                表格视图
              </el-button>
              <el-button
                :type="tableView === 'card' ? 'primary' : ''"
                @click="tableView = 'card'"
                :icon="Collection"
              >
                卡片视图
              </el-button>
            </el-button-group>
            <el-button
              type="primary"
              size="small"
              @click="exportData"
              :loading="exportLoading"
              :icon="Download"
            >
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="tableView === 'table'" class="table-view">
        <el-table
          :data="taskDetails"
          v-loading="loading"
          class="task-table"
          :row-class-name="getRowClassName"
          @row-click="handleRowClick"
        >
          <el-table-column prop="projectName" label="项目名称" min-width="150" fixed="left">
            <template #default="{ row }">
              <div class="project-cell">
                <el-icon class="project-icon"><Document /></el-icon>
                <span class="project-name">{{ row.projectName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="orderNumber" label="订单编号" width="140">
            <template #default="{ row }">
              <div class="order-cell">
                <el-tag size="small" type="info">{{ row.orderNumber }}</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="totalPrice" label="项目总价" width="140" sortable>
            <template #default="{ row }">
              <div class="amount-cell">
                <el-icon class="amount-icon"><Money /></el-icon>
                <span class="amount-value">¥{{ formatNumber(row.totalPrice) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="netIncome" label="实得收入" width="140" sortable>
            <template #default="{ row }">
              <div class="amount-cell success">
                <el-icon class="amount-icon"><Wallet /></el-icon>
                <span class="amount-value">¥{{ formatNumber(row.netIncome) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="statusName" label="状态" width="120">
            <template #default="{ row }">
              <div class="status-cell">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ row.statusName }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="orderTime" label="接单时间" width="160" sortable>
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span>{{ formatDate(row.orderTime) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="createdByName" label="派单人" width="120">
            <template #default="{ row }">
              <div class="user-cell">
                <el-avatar :size="24" class="user-avatar-mini">
                  {{ row.createdByName?.charAt(0) || 'U' }}
                </el-avatar>
                <span>{{ row.createdByName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="remarks" label="备注" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="remarks-cell">
                <span>{{ row.remarks || '无备注' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button size="small" text @click="viewTaskDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="task-cards">
          <div
            v-for="(task, index) in taskDetails"
            :key="index"
            class="task-card-item"
            @click="handleRowClick(task)"
          >
            <div class="task-card-header">
              <div class="task-project">
                <el-icon class="project-icon"><Document /></el-icon>
                <span class="project-name">{{ task.projectName }}</span>
              </div>
              <div class="task-status">
                <el-tag :type="getStatusType(task.status)" size="small">
                  {{ task.statusName }}
                </el-tag>
              </div>
            </div>

            <div class="task-card-body">
              <div class="task-order">
                <span class="order-label">订单编号：</span>
                <el-tag size="small" type="info">{{ task.orderNumber }}</el-tag>
              </div>

              <div class="task-amounts">
                <div class="amount-row">
                  <div class="amount-item">
                    <el-icon class="amount-icon"><Money /></el-icon>
                    <span class="amount-label">项目总价</span>
                    <span class="amount-value">¥{{ formatNumber(task.totalPrice) }}</span>
                  </div>
                  <div class="amount-item success">
                    <el-icon class="amount-icon"><Wallet /></el-icon>
                    <span class="amount-label">实得收入</span>
                    <span class="amount-value">¥{{ formatNumber(task.netIncome) }}</span>
                  </div>
                </div>
              </div>

              <div class="task-meta">
                <div class="meta-item">
                  <el-icon><Clock /></el-icon>
                  <span>{{ formatDate(task.orderTime) }}</span>
                </div>
                <div class="meta-item">
                  <el-icon><User /></el-icon>
                  <span>{{ task.createdByName }}</span>
                </div>
              </div>

              <div v-if="task.remarks" class="task-remarks">
                <el-icon><ChatDotRound /></el-icon>
                <span>{{ task.remarks }}</span>
              </div>
            </div>

            <div class="task-card-footer">
              <el-button size="small" text @click.stop="viewTaskDetail(task)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空数据状态 -->
      <div v-if="!taskDetails || taskDetails.length === 0" class="no-task-data">
        <el-icon class="no-data-icon"><Document /></el-icon>
        <div class="no-data-text">暂无任务数据</div>
        <div class="no-data-desc">请调整筛选条件后重试</div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  Filter,
  Calendar,
  Clock,
  DocumentChecked,
  Timer,
  DataAnalysis,
  TrendCharts,
  Money,
  Wallet,
  Document,
  PieChart,
  ArrowUp,
  ArrowDown,
  DataLine,
  Grid,
  Collection,
  CreditCard,
  View,
  User,
  ChatDotRound
} from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'
import { statisticsApi, StatisticsQueryBuilder } from '../../api/statistics'
import { statisticsManager } from '../../utils/statisticsManager'
import { processChartData } from '../../utils/chartDataProcessor'
import IncomeChart from '../../components/charts/IncomeChart.vue'
import StatisticsCard from '../../components/statistics/StatisticsCard.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const chartType = ref('line')
const dateRange = ref([])

// 查询表单
const queryForm = reactive({
  timeDimension: 'month',
  startTime: null,
  endTime: null,
  onlySettled: false
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const statisticsData = ref({
  overall: null,
  trendData: [],
  chartData: null
})

// 任务详情
const taskDetails = ref([])

// 筛选卡片折叠状态
const filterCollapsed = ref(false)

// 表格视图切换
const tableView = ref('table')

// 计算属性
const completionRate = computed(() => {
  if (!statisticsData.value.overall) return 0
  const { totalTaskCount, completedTaskCount, settledTaskCount } = statisticsData.value.overall
  if (totalTaskCount === 0) return 0
  return ((completedTaskCount + settledTaskCount) / totalTaskCount * 100).toFixed(1)
})

const chartData = computed(() => {
  if (!statisticsData.value || !statisticsData.value.trendData) {
    return {
      xAxisLabels: [],
      totalAmountSeries: [],
      netIncomeSeries: [],
      taskCountSeries: []
    }
  }

  return processChartData(statisticsData.value)
})

const taskStatusData = computed(() => {
  if (!statisticsData.value.overall) return []
  
  const { totalTaskCount, completedTaskCount, settledTaskCount } = statisticsData.value.overall
  const inProgressCount = totalTaskCount - completedTaskCount - settledTaskCount
  
  const data = [
    { name: '已结算', count: settledTaskCount, color: '#67C23A' },
    { name: '已完成', count: completedTaskCount, color: '#409EFF' },
    { name: '进行中', count: inProgressCount, color: '#E6A23C' }
  ]
  
  return data.map(item => ({
    ...item,
    percentage: totalTaskCount > 0 ? (item.count / totalTaskCount * 100).toFixed(1) : 0
  }))
})

// 页面初始化
onMounted(() => {
  initPage()
})

async function initPage() {
  // 恢复筛选卡片折叠状态
  const savedCollapsed = localStorage.getItem('my-income-statistics-filter-collapsed')
  if (savedCollapsed !== null) {
    filterCollapsed.value = savedCollapsed === 'true'
  }

  // 设置默认时间范围（最近3个月）
  setQuickRange('quarter')

  // 加载统计数据
  await loadStatistics()
  await loadTaskDetails()
}

// 加载统计数据
async function loadStatistics() {
  loading.value = true
  try {
    const query = StatisticsQueryBuilder.create()
    StatisticsQueryBuilder.setTimeDimension(query, queryForm.timeDimension)
    StatisticsQueryBuilder.setTimeRange(query, queryForm.startTime, queryForm.endTime)
    StatisticsQueryBuilder.setOnlySettled(query, queryForm.onlySettled)

    const response = await statisticsApi.getMyIncomeStatistics(query)
    statisticsData.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 加载任务详情
async function loadTaskDetails() {
  try {
    const query = StatisticsQueryBuilder.create()
    StatisticsQueryBuilder.setTimeRange(query, queryForm.startTime, queryForm.endTime)
    StatisticsQueryBuilder.setOnlySettled(query, queryForm.onlySettled)

    const response = await statisticsApi.getMyIncomeDetail(query)
    taskDetails.value = response.data.taskDetails || []
    pagination.total = taskDetails.value.length
  } catch (error) {
    ElMessage.error('加载任务详情失败: ' + (error.message || '未知错误'))
  }
}

// 时间维度变化处理
function handleTimeDimensionChange() {
  loadStatistics()
}

// 日期范围变化处理
function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    queryForm.startTime = value[0]
    queryForm.endTime = value[1]
  } else {
    queryForm.startTime = null
    queryForm.endTime = null
  }
}

// 快速时间范围设置
function setQuickRange(type) {
  const now = new Date()
  let startTime, endTime

  switch (type) {
    case 'week':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'month':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'quarter':
      startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'year':
      startTime = new Date(now.getFullYear(), 0, 1)
      endTime = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
      break
  }

  queryForm.startTime = formatDateTime(startTime)
  queryForm.endTime = formatDateTime(endTime)
  dateRange.value = [queryForm.startTime, queryForm.endTime]
}

// 重置筛选条件
function resetFilters() {
  queryForm.timeDimension = 'month'
  queryForm.onlySettled = false
  setQuickRange('quarter')
  loadStatistics()
  loadTaskDetails()
}

// 导出数据
async function exportData() {
  exportLoading.value = true
  try {
    // 这里可以实现导出功能
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败: ' + (error.message || '未知错误'))
  } finally {
    exportLoading.value = false
  }
}

// 表格行点击
function handleRowClick(row) {
  console.log('点击任务:', row)
  // 可以在这里实现跳转到任务详情页面
}

// 分页处理
function handleSizeChange(size) {
  pagination.size = size
  pagination.page = 1
}

function handleCurrentChange(page) {
  pagination.page = page
}

// 获取状态类型
function getStatusType(status) {
  const statusMap = {
    0: 'info',     // 待处理
    1: 'warning',  // 进行中
    2: 'success',  // 已完成
    3: 'danger',   // 客户已取消
    4: 'warning',  // 待结算
    5: 'success'   // 已结算
  }
  return statusMap[status] || 'info'
}

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 格式化日期时间
function formatDateTime(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化数字
function formatNumber(num) {
  if (!num) return '0'
  return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 })
}

// 格式化统计周期
function formatPeriod() {
  if (!queryForm.startTime || !queryForm.endTime) return '全部时间'
  const start = new Date(queryForm.startTime).toLocaleDateString('zh-CN')
  const end = new Date(queryForm.endTime).toLocaleDateString('zh-CN')
  return `${start} - ${end}`
}

// 切换筛选卡片折叠状态
function toggleFilterCollapse() {
  filterCollapsed.value = !filterCollapsed.value

  // 保存折叠状态到本地存储
  localStorage.setItem('my-income-statistics-filter-collapsed', filterCollapsed.value.toString())
}

// 获取表格行样式
function getRowClassName({ rowIndex }) {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取进度条颜色
function getProgressColor(percentage) {
  if (percentage >= 80) return '#67C23A'
  if (percentage >= 60) return '#E6A23C'
  if (percentage >= 40) return '#409EFF'
  return '#F56C6C'
}

// 查看任务详情
function viewTaskDetail(task) {
  console.log('查看任务详情:', task)
  // 可以在这里实现查看详情的逻辑
}

// 获取筛选条件摘要
function getFilterSummary() {
  const summary = []

  // 时间维度
  const timeDimensionMap = {
    year: '按年',
    month: '按月',
    week: '按周',
    day: '按日'
  }
  summary.push(timeDimensionMap[queryForm.timeDimension] || '按月')

  // 时间范围
  if (queryForm.startTime && queryForm.endTime) {
    const start = new Date(queryForm.startTime).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    const end = new Date(queryForm.endTime).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    summary.push(`${start} - ${end}`)
  }

  // 统计范围
  summary.push(queryForm.onlySettled ? '仅已结算' : '已完成+已结算')

  return summary.join(' | ')
}
</script>

<style scoped>
.my-income-statistics {
  padding: 24px;
  min-height: calc(100vh - 60px);
}

/* 个人信息卡片样式 */
.user-profile-card {
  margin-bottom: 24px;
}

.profile-card {
  border-radius: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-content {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 8px;
}

.profile-avatar {
  position: relative;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 32px;
  font-weight: 600;
}

.online-badge {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #67C23A;
  border: 3px solid white;
  border-radius: 50%;
}

.profile-info {
  flex: 1;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #303133;
}

.user-role {
  margin: 0 0 16px 0;
  color: #909399;
  font-size: 14px;
}

.user-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.filter-icon {
  color: #667eea;
}

.filter-summary {
  margin-left: 12px;
  font-weight: normal;
  font-size: 12px;
}

.collapse-btn {
  padding: 4px 8px;
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  background-color: rgba(102, 126, 234, 0.1);
  border-radius: 6px;
}

.collapse-icon {
  transition: transform 0.3s ease;
  color: #667eea;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

.filter-content {
  margin-top: 16px;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-group {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.filter-select,
.filter-date-picker {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.filter-select:hover,
.filter-date-picker:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.quick-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
}

.quick-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.quick-buttons {
  display: flex;
  gap: 8px;
}

.quick-btn {
  border-radius: 20px;
  padding: 6px 16px;
  transition: all 0.3s ease;
  border: 1px solid #667eea;
  color: #667eea;
}

.quick-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 折叠状态样式 */
.collapsed-quick-actions {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-buttons-collapsed {
  display: flex;
  gap: 8px;
  flex: 1;
  flex-wrap: wrap;
}

.quick-btn-collapsed {
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.3s ease;
  border: 1px solid #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.quick-btn-collapsed:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.collapsed-main-actions {
  display: flex;
  gap: 8px;
}

.filter-actions-bottom {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .collapsed-quick-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .quick-buttons-collapsed {
    justify-content: center;
  }

  .collapsed-main-actions {
    justify-content: center;
  }
}

/* 个人统计样式 */
.personal-stats {
  margin-bottom: 24px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: black;
}

.stats-period {
  display: flex;
  align-items: center;
  gap: 8px;
}

.personal-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.personal-card {
  position: relative;
  padding: 24px;
  border-radius: 20px;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.personal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--card-gradient);
  opacity: 0.1;
  z-index: 0;
}

.personal-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.primary-gradient {
  --card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.success-gradient {
  --card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.warning-gradient {
  --card-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.info-gradient {
  --card-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--card-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #67C23A;
  background: rgba(103, 194, 58, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.trend-up {
  font-size: 14px;
}

.card-body {
  position: relative;
  z-index: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: 500;
}

.filter-actions {
  margin-left: auto;
}

.quick-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.quick-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card,
.status-card,
.table-card {
  border-radius: 8px;
  border: none;
}

.chart-header,
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.status-list {
  padding: 16px 0;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-info {
  flex: 1;
}

.status-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-count {
  font-size: 12px;
  color: #909399;
}

.status-progress {
  width: 120px;
  margin-left: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .my-income-statistics {
    padding: 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filter-actions {
    margin-left: 0;
  }

  .quick-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .statistics-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-header,
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .status-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .status-progress {
    width: 100%;
    margin-left: 0;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tbody tr:hover > td) {
  background-color: #f8f9fa;
}

/* 卡片阴影效果 */
.filter-card,
.chart-card,
.status-card,
.table-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s ease;
}

.filter-card:hover,
.chart-card:hover,
.status-card:hover,
.table-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 表格和卡片视图样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-icon {
  color: #667eea;
  font-size: 18px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-view {
  margin-top: 16px;
}

.task-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 表格单元格样式 */
.project-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-icon {
  color: #667eea;
  font-size: 14px;
}

.project-name {
  font-weight: 600;
  color: #303133;
}

.order-cell {
  display: flex;
  justify-content: center;
}

.amount-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-cell.success .amount-icon {
  color: #67C23A;
}

.amount-icon {
  color: #667eea;
  font-size: 14px;
}

.amount-value {
  font-weight: 600;
}

.status-cell {
  display: flex;
  justify-content: center;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-icon {
  color: #909399;
  font-size: 14px;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar-mini {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.remarks-cell {
  color: #606266;
  font-size: 14px;
}

/* 表格行样式 */
:deep(.task-table .even-row) {
  background-color: #fafafa;
}

:deep(.task-table .odd-row) {
  background-color: white;
}

:deep(.task-table .el-table__row:hover) {
  background-color: #f0f9ff !important;
}

/* 卡片视图样式 */
.card-view {
  margin-top: 16px;
}

.task-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.task-card-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f0f0f0;
}

.task-card-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  border-color: #667eea;
}

.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.task-project {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.task-project .project-icon {
  color: #667eea;
  font-size: 16px;
}

.task-project .project-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.task-status {
  flex-shrink: 0;
}

.task-card-body {
  margin-bottom: 16px;
}

.task-order {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.order-label {
  color: #909399;
  font-size: 14px;
}

.task-amounts {
  margin-bottom: 12px;
}

.amount-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.amount-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4ff 100%);
  border: 1px solid #e1ecff;
}

.amount-item.success {
  background: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 100%);
  border: 1px solid #b3e5fc;
}

.amount-item .amount-icon {
  font-size: 16px;
  margin-bottom: 4px;
}

.amount-item .amount-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.amount-item .amount-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.task-remarks {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}

.task-card-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 空数据状态 */
.no-task-data {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
}

.no-task-data .no-data-icon {
  font-size: 64px;
  color: #dcdfe6;
  margin-bottom: 20px;
}

.no-task-data .no-data-text {
  font-size: 18px;
  color: #606266;
  margin-bottom: 8px;
}

.no-task-data .no-data-desc {
  font-size: 14px;
  color: #909399;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .collapsed-quick-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .quick-buttons-collapsed {
    justify-content: center;
  }

  .collapsed-main-actions {
    justify-content: center;
  }

  .task-cards {
    grid-template-columns: 1fr;
  }

  .amount-row {
    grid-template-columns: 1fr;
  }

  .task-meta {
    flex-direction: column;
    gap: 8px;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
  }

  .table-actions {
    justify-content: center;
  }
}
</style>
