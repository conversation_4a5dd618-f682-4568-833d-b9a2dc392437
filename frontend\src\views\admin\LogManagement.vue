<template>
  <div class="log-management">
    <!-- 标签页导航 -->
    <el-tabs v-model="activeTab" class="log-tabs">
      <el-tab-pane label="操作日志" name="action">
        <ActionLogManagement />
      </el-tab-pane>
      <el-tab-pane label="登录日志" name="login">
        <LoginLogManagement />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ActionLogManagement from './ActionLogManagement.vue'
import LoginLogManagement from './LoginLogManagement.vue'

// 当前激活的标签页
const activeTab = ref('action')
</script>

<style scoped>
.log-management {
  padding: 0;
  background: #f5f7fa;
  min-height: 100vh;
}

.log-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
