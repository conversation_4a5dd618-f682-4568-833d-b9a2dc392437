package com.haitao.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.dto.TaskListResponse;
import com.haitao.backend.dto.TaskQueryRequest;
import com.haitao.backend.dto.StatisticsQueryRequest;
import com.haitao.backend.dto.IncomeStatisticsResponse;
import com.haitao.backend.dto.UserIncomeDetailResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 订单任务Mapper接口
 */
@Mapper
public interface OrderTaskMapper extends BaseMapper<OrderTask> {
    
    /**
     * 分页查询任务列表（带用户信息）
     */
    IPage<TaskListResponse> selectTaskListWithUserInfo(
        Page<TaskListResponse> page, 
        @Param("query") TaskQueryRequest query
    );
    
    /**
     * 根据ID查询任务详情（带用户信息）
     */
    TaskListResponse selectTaskDetailById(@Param("id") Long id);
    
    /**
     * 生成订单编号
     */
    String generateOrderNumber();

    // ==================== 统计查询方法 ====================

    /**
     * 查询总体统计数据
     */
    IncomeStatisticsResponse.OverallStatistics selectOverallStatistics(@Param("query") StatisticsQueryRequest query);

    /**
     * 查询时间趋势数据
     */
    List<IncomeStatisticsResponse.TrendData> selectTrendData(@Param("query") StatisticsQueryRequest query);

    /**
     * 查询用户排行数据
     */
    List<IncomeStatisticsResponse.UserRankingData> selectUserRankingData(@Param("query") StatisticsQueryRequest query);

    /**
     * 查询指定用户的收入详情
     */
    UserIncomeDetailResponse.IncomeSummary selectUserIncomeSummary(@Param("userId") Long userId, @Param("query") StatisticsQueryRequest query);

    /**
     * 查询指定用户的任务收入详情列表
     */
    List<UserIncomeDetailResponse.TaskIncomeDetail> selectUserTaskIncomeDetails(@Param("userId") Long userId, @Param("query") StatisticsQueryRequest query);

    /**
     * 按月统计收入趋势
     */
    List<Map<String, Object>> selectMonthlyIncomeTrend(@Param("query") StatisticsQueryRequest query);

    /**
     * 按周统计收入趋势
     */
    List<Map<String, Object>> selectWeeklyIncomeTrend(@Param("query") StatisticsQueryRequest query);

    /**
     * 按日统计收入趋势
     */
    List<Map<String, Object>> selectDailyIncomeTrend(@Param("query") StatisticsQueryRequest query);

    /**
     * 统计派单员抽成排行
     */
    List<Map<String, Object>> selectDispatcherFeeRanking(@Param("query") StatisticsQueryRequest query);
}
