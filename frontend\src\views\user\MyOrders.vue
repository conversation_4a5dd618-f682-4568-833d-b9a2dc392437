<template>
  <div class="my-orders">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索项目名称或订单编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="待处理" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="客户已取消" :value="3" />
            <el-option label="待结算" :value="4" />
            <el-option label="已结算" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getOrderList" :loading="loading">
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 订单列表 -->
    <div class="table-section">
      <el-table
        :data="orderList"
        v-loading="loading"
        stripe
        @selection-change="handleSelectionChange"
        class="order-table"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNumber" label="订单编号" width="180" />
        <el-table-column prop="projectName" label="项目名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="totalPrice" label="订单金额" width="120" align="right">
          <template #default="{ row }">
            <span class="price-text">¥{{ formatNumber(row.totalPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="commissionAmount" label="抽成金额" width="120" align="right">
          <template #default="{ row }">
            <span class="commission-text">¥{{ formatNumber(row.commissionAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="netIncome" label="实得收入" width="120" align="right">
          <template #default="{ row }">
            <span class="income-text">¥{{ formatNumber(row.netIncome) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderTime" label="下单时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.orderTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="deadline" label="截止时间" width="150">
          <template #default="{ row }">
            <span :class="getDeadlineClass(row.deadline, row.status)">
              {{ formatDateTime(row.deadline) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="170" align="center">
          <template #default="{ row }">
            <div class="status-container">
              <!-- 状态标签显示 -->
              <div class="status-badge" :class="`status-${row.status}`">
                <span class="status-icon">{{ getStatusIcon(row.status) }}</span>
                <span class="status-text">{{ getStatusText(row.status) }}</span>
              </div>

              <!-- 快速更改按钮 -->
              <el-dropdown
                @command="handleQuickStatusChange(row, $event)"
                trigger="click"
                placement="bottom"
              >
                <el-button
                  size="small"
                  type="text"
                  class="status-change-btn"
                >
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="status in statusOptions"
                      :key="status.value"
                      :command="status.value"
                      :disabled="status.value === row.status"
                      :class="`dropdown-item-${status.value}`"
                    >
                      <span class="dropdown-status-option">
                        <span class="dropdown-icon">{{ status.icon }}</span>
                        <span class="dropdown-text">{{ status.label }}</span>
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.status === 0" 
              size="small" 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentOrderDetail" class="order-detail">
        <!-- 订单状态卡片 -->
        <div class="status-card">
          <div class="status-header">
            <div class="status-info">
              <h2 class="project-title">{{ currentOrderDetail.projectName }}</h2>
              <p class="order-number">订单编号：{{ currentOrderDetail.orderNumber }}</p>
            </div>
            <div class="status-badge">
              <el-tag
                :type="getStatusType(currentOrderDetail.status)"
                size="large"
                effect="dark"
              >
                {{ getStatusText(currentOrderDetail.status) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 时间信息卡片 -->
        <div class="time-card">
          <div class="time-item">
            <div class="time-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="time-content">
              <div class="time-label">接单时间</div>
              <div class="time-value">{{ formatDateTime(currentOrderDetail.orderTime) }}</div>
            </div>
          </div>
          <div class="time-item" v-if="currentOrderDetail.deadline">
            <div class="time-icon deadline">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="time-content">
              <div class="time-label">截止时间</div>
              <div class="time-value" :class="getDeadlineClass(currentOrderDetail.deadline, currentOrderDetail.status)">
                {{ formatDateTime(currentOrderDetail.deadline) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 金额信息 -->
        <div class="finance-section">
          <div class="finance-header">
            <h3><el-icon><Money /></el-icon>财务信息</h3>
          </div>
          <div class="finance-grid">
            <div class="finance-card total">
              <div class="finance-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="finance-content">
                <div class="finance-label">订单总金额</div>
                <div class="finance-value">¥{{ formatNumber(currentOrderDetail.totalPrice) }}</div>
              </div>
            </div>

            <div class="finance-card rate">
              <div class="finance-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="finance-content">
                <div class="finance-label">抽成比例</div>
                <div class="finance-value">{{ formatPercent(currentOrderDetail.commissionRate) }}</div>
              </div>
            </div>

            <div class="finance-card commission">
              <div class="finance-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="finance-content">
                <div class="finance-label">抽成金额</div>
                <div class="finance-value">¥{{ formatNumber(currentOrderDetail.commissionAmount) }}</div>
              </div>
            </div>

            <div class="finance-card income">
              <div class="finance-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="finance-content">
                <div class="finance-label">实得收入</div>
                <div class="finance-value">¥{{ formatNumber(currentOrderDetail.netIncome) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="remarks-section" v-if="currentOrderDetail.remarks">
          <div class="remarks-header">
            <h3><el-icon><Document /></el-icon>备注信息</h3>
          </div>
          <div class="remarks-content">
            <p>{{ currentOrderDetail.remarks }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新建/编辑订单对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑订单' : '新建订单'"
      width="800px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="orderForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="orderForm.projectName"
                placeholder="请输入项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单编号">
              <el-input
                v-model="orderForm.orderNumber"
                placeholder="留空自动生成（格式：ORD20250727001）"
                clearable
              />
              <div class="form-tip">留空将自动生成订单编号</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目总价" prop="totalPrice">
              <el-input-number
                v-model="orderForm.totalPrice"
                :min="0.01"
                :precision="2"
                placeholder="请输入项目总价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="抽成比例" prop="commissionRate">
              <el-input-number
                v-model="orderForm.commissionRate"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                placeholder="请输入抽成比例"
                style="width: 100%"
              />
              <div class="form-tip">输入0-1之间的数值，如0.1表示10%抽成</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="派单员抽成">
              <el-input-number
                v-model="orderForm.dispatcherFee"
                :min="0"
                :precision="2"
                placeholder="自动计算"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分配成员">
              <el-input
                :value="userStore.userInfo.realName || userStore.userInfo.username"
                readonly
                style="width: 100%"
              />
              <div class="form-tip">用户端创建订单自动分配给自己</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接单时间" prop="orderTime">
              <el-date-picker
                v-model="orderForm.orderTime"
                type="datetime"
                placeholder="选择接单时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="orderForm.deadline"
                type="datetime"
                placeholder="选择截止时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDeadlineDate"
                :disabled-time="disabledDeadlineTime"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单状态" prop="status">
              <el-select v-model="orderForm.status" placeholder="选择订单状态" style="width: 100%">
                <el-option label="待处理" :value="0" />
                <el-option label="进行中" :value="1" />
                <el-option label="已完成" :value="2" />
                <el-option label="客户已取消" :value="3" />
                <el-option label="待结算" :value="4" />
                <el-option label="已结算" :value="5" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注信息">
          <el-input
            v-model="orderForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 收入计算预览 -->
        <div v-if="orderForm.totalPrice && orderForm.commissionRate !== null" class="income-preview">
          <el-alert
            title="收入计算预览"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>项目总价：¥{{ orderForm.totalPrice?.toFixed(2) }}</p>
              <p>抽成比例：{{ (orderForm.commissionRate * 100).toFixed(1) }}%</p>
              <p>抽成金额：¥{{ (orderForm.totalPrice * orderForm.commissionRate).toFixed(2) }}</p>
              <p>派单员抽成：¥{{ (orderForm.dispatcherFee || 0).toFixed(2) }}</p>
              <p><strong>实得收入：¥{{ (orderForm.totalPrice * (1 - orderForm.commissionRate)).toFixed(2) }}</strong></p>
            </template>
          </el-alert>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, Calendar, Money, TrendCharts, Document, ArrowDown } from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'
import { orderApi } from '../../api/order'


const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const selectedOrders = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const formDialogVisible = ref(false)
const currentOrderId = ref(null)
const currentOrderDetail = ref(null)
const isEdit = ref(false)
const submitting = ref(false)

// 表单引用和数据
const formRef = ref()
const orderForm = reactive({
  projectName: '',
  orderNumber: '',
  totalPrice: null,
  commissionRate: null,
  dispatcherFee: 0,
  assignedUserId: userStore.userInfo.id,
  orderTime: '',
  deadline: '',
  status: 0,
  remarks: ''
})

// 表单验证规则
const formRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  totalPrice: [
    { required: true, message: '请输入项目总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '项目总价必须大于0', trigger: 'blur' }
  ],
  commissionRate: [
    { required: true, message: '请输入抽成比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '抽成比例必须在0-1之间', trigger: 'blur' }
  ],
  orderTime: [
    { required: true, message: '请选择接单时间', trigger: 'change' }
  ]
}

// 状态选项数据
const statusOptions = [
  { value: 0, label: '待处理', icon: '⏳' },
  { value: 1, label: '进行中', icon: '🔄' },
  { value: 2, label: '已完成', icon: '✅' },
  { value: 3, label: '客户已取消', icon: '❌' },
  { value: 4, label: '待结算', icon: '💰' },
  { value: 5, label: '已结算', icon: '💳' }
]

// 获取状态图标
function getStatusIcon(status) {
  const statusMap = {
    0: '⏳',
    1: '🔄',
    2: '✅',
    3: '❌',
    4: '💰',
    5: '💳'
  }
  return statusMap[status] || '❓'
}

// 计算派单员抽成（等于抽成金额，需要保存两份相同数据）
const calculatedDispatcherFee = computed(() => {
  if (orderForm.totalPrice && orderForm.commissionRate !== null) {
    return Number((orderForm.totalPrice * orderForm.commissionRate).toFixed(2))
  }
  return 0
})

// 监听计算结果，自动更新派单员抽成
watch(calculatedDispatcherFee, (newValue) => {
  orderForm.dispatcherFee = newValue
}, { immediate: true })

// 获取订单列表
async function getOrderList() {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status !== null ? searchForm.status : undefined
    }

    const response = await orderApi.getMyOrderList(params)
    if (response.code === 200) {
      orderList.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
function resetSearch() {
  searchForm.keyword = ''
  searchForm.status = null
  pagination.current = 1
  getOrderList()
}

// 处理选择变化
function handleSelectionChange(selection) {
  selectedOrders.value = selection
}



// 查看订单详情
async function handleView(row) {
  currentOrderId.value = row.id

  try {
    const response = await orderApi.getOrderDetail(row.id)
    if (response.code === 200) {
      currentOrderDetail.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

// 新建订单
function handleCreate() {
  resetForm()
  isEdit.value = false
  // 设置默认接单时间为当前时间
  orderForm.orderTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
  formDialogVisible.value = true
}

// 编辑订单
async function handleEdit(row) {
  resetForm()
  isEdit.value = true
  currentOrderId.value = row.id

  try {
    const response = await orderApi.getOrderDetail(row.id)
    if (response.code === 200) {
      const data = response.data
      // 复制数据到表单，排除系统字段
      const excludeFields = ['id', 'createdBy', 'createTime', 'updateTime', 'isDeleted']
      Object.keys(orderForm).forEach(key => {
        if (data[key] !== undefined && !excludeFields.includes(key)) {
          orderForm[key] = data[key]
        }
      })

      // 格式化时间字段为表单可用的格式
      if (data.orderTime) {
        orderForm.orderTime = new Date(data.orderTime).toISOString().slice(0, 19).replace('T', ' ')
      }
      if (data.deadline) {
        orderForm.deadline = new Date(data.deadline).toISOString().slice(0, 19).replace('T', ' ')
      }

      formDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

// 删除订单
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单"${row.projectName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await orderApi.deleteOrder(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getOrderList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 快速状态更改
async function handleQuickStatusChange(row, newStatus) {
  try {
    const response = await orderApi.updateOrder(row.id, {
      status: newStatus
    })

    if (response.code === 200) {
      ElMessage.success('状态更新成功')
      // 更新本地数据
      row.status = newStatus
    } else {
      ElMessage.error(response.message || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

// 时间选择限制
const disabledDeadlineDate = (time) => {
  if (!orderForm.orderTime) return false
  return time.getTime() < new Date(orderForm.orderTime).getTime()
}

const disabledDeadlineTime = (date) => {
  if (!orderForm.orderTime) return {}
  const orderDate = new Date(orderForm.orderTime)
  const selectedDate = new Date(date)

  if (selectedDate.toDateString() === orderDate.toDateString()) {
    return {
      disabledHours: () => {
        const hours = []
        for (let i = 0; i < orderDate.getHours(); i++) {
          hours.push(i)
        }
        return hours
      },
      disabledMinutes: (hour) => {
        if (hour === orderDate.getHours()) {
          const minutes = []
          for (let i = 0; i <= orderDate.getMinutes(); i++) {
            minutes.push(i)
          }
          return minutes
        }
        return []
      }
    }
  }
  return {}
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(orderForm, {
    projectName: '',
    orderNumber: '',
    totalPrice: null,
    commissionRate: null,
    dispatcherFee: 0,
    assignedUserId: userStore.userInfo.id,
    orderTime: '',
    deadline: '',
    status: 0,
    remarks: ''
  })
  currentOrderId.value = null
  isEdit.value = false
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate()

    // 验证时间逻辑
    if (orderForm.orderTime && orderForm.deadline) {
      if (new Date(orderForm.orderTime) >= new Date(orderForm.deadline)) {
        ElMessage.error('截止时间必须晚于接单时间')
        return
      }
    }

    submitting.value = true

    // 准备提交数据
    const submitData = { ...orderForm }

    // 如果没有设置接单时间，自动设置为当前时间
    if (!submitData.orderTime) {
      submitData.orderTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
    }

    let response
    if (isEdit.value) {
      response = await orderApi.updateOrder(currentOrderId.value, submitData)
    } else {
      response = await orderApi.createOrder(submitData)
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      formDialogVisible.value = false
      getOrderList()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    if (error !== false) { // 表单验证失败时不显示错误
      console.error('提交失败:', error)
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 分页大小变化
function handleSizeChange(size) {
  pagination.size = size
  pagination.current = 1
  getOrderList()
}

// 当前页变化
function handleCurrentChange(current) {
  pagination.current = current
  getOrderList()
}

// 获取状态类型
function getStatusType(status) {
  const statusMap = {
    0: 'info',     // 待处理
    1: 'warning',  // 进行中
    2: 'success',  // 已完成
    3: 'danger',   // 客户已取消
    4: 'warning',  // 待结算
    5: 'success'   // 已结算
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    0: '待处理',
    1: '进行中',
    2: '已完成',
    3: '客户已取消',
    4: '待结算',
    5: '已结算'
  }
  return statusMap[status] || '未知'
}

// 格式化数字
function formatNumber(num) {
  if (!num) return '0.00'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化百分比
function formatPercent(rate) {
  if (!rate) return '0.0%'
  return (Number(rate) * 100).toFixed(1) + '%'
}

// 获取截止时间样式类
function getDeadlineClass(deadline, status) {
  if (!deadline) return ''

  // 如果订单已完成或已取消，不需要特殊样式
  if (status === 2 || status === 3 || status === 5) return ''

  const now = new Date()
  const deadlineDate = new Date(deadline)
  const timeDiff = deadlineDate.getTime() - now.getTime()
  const daysDiff = timeDiff / (1000 * 3600 * 24)

  if (timeDiff < 0) {
    return 'deadline-overdue' // 已过期
  } else if (daysDiff <= 1) {
    return 'deadline-urgent' // 紧急（1天内）
  } else if (daysDiff <= 3) {
    return 'deadline-warning' // 警告（3天内）
  }
  return ''
}





// 页面初始化
onMounted(() => {
  getOrderList()
})
</script>

<style scoped>
.my-orders {
  padding: 20px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.order-table {
  width: 100%;
}

.pagination-wrapper {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.price-text {
  color: #409EFF;
  font-weight: 600;
}

.income-text {
  color: #67C23A;
  font-weight: 600;
}

.commission-text {
  color: #E6A23C;
  font-weight: 600;
}

/* 截止时间样式 */
.deadline-overdue {
  color: #F56C6C;
  font-weight: 600;
}

.deadline-urgent {
  color: #E6A23C;
  font-weight: 600;
}

.deadline-warning {
  color: #F56C6C;
}

/* 表单样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.income-preview {
  margin-top: 20px;
}

.income-preview :deep(.el-alert__content) {
  line-height: 1.6;
}

.income-preview p {
  margin: 4px 0;
  font-size: 14px;
}

.income-preview strong {
  color: #409eff;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

/* 订单详情样式 */
.order-detail {
  padding: 0;
}

/* 状态卡片 */
.status-card {
  background: linear-gradient(135deg, #2dd4bf 0%, #0ea5e9 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  color: white;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.order-number {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.status-badge .el-tag {
  font-size: 14px;
  padding: 8px 16px;
  border: none;
}

/* 时间信息卡片 */
.time-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 24px;
}

.time-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.time-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: #409EFF;
  font-size: 20px;
}

.time-icon.deadline {
  background: #fef3e2;
  color: #E6A23C;
}

.time-content {
  flex: 1;
}

.time-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.time-value {
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

/* 财务信息 */
.finance-section {
  margin-bottom: 20px;
}

.finance-header {
  margin-bottom: 16px;
}

.finance-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.finance-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.finance-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.finance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.finance-card.total {
  border-left-color: #409EFF;
}

.finance-card.rate {
  border-left-color: #909399;
}

.finance-card.commission {
  border-left-color: #E6A23C;
}

.finance-card.income {
  border-left-color: #67C23A;
}

.finance-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.finance-card.total .finance-icon {
  background: #f0f9ff;
  color: #409EFF;
}

.finance-card.rate .finance-icon {
  background: #f5f5f5;
  color: #909399;
}

.finance-card.commission .finance-icon {
  background: #fef3e2;
  color: #E6A23C;
}

.finance-card.income .finance-icon {
  background: #f0f9f0;
  color: #67C23A;
}

.finance-content {
  flex: 1;
}

.finance-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.finance-value {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
}

/* 状态容器 */
.status-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 状态图标 */
.status-icon {
  font-size: 14px;
  line-height: 1;
}

/* 状态文字 */
.status-text {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 不同状态的颜色 */
.status-badge.status-0 {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-color: #adb5bd;
  color: #6c757d;
}

.status-badge.status-1 {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #2196f3;
  color: #1976d2;
}

.status-badge.status-2 {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-color: #4caf50;
  color: #2e7d32;
}

.status-badge.status-3 {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-color: #f44336;
  color: #c62828;
}

.status-badge.status-4 {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-color: #ff9800;
  color: #ef6c00;
}

.status-badge.status-5 {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-color: #4caf50;
  color: #2e7d32;
}

/* 状态更改按钮 */
.status-change-btn {
  padding: 4px !important;
  min-height: 24px !important;
  width: 24px !important;
  border-radius: 50% !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.status-change-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: scale(1.1) !important;
}

/* 下拉菜单项 */
.dropdown-status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.dropdown-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.dropdown-text {
  font-weight: 500;
}

/* 下拉菜单项颜色 */
.dropdown-item-0 .dropdown-text { color: #6c757d; }
.dropdown-item-1 .dropdown-text { color: #1976d2; }
.dropdown-item-2 .dropdown-text { color: #2e7d32; }
.dropdown-item-3 .dropdown-text { color: #c62828; }
.dropdown-item-4 .dropdown-text { color: #ef6c00; }
.dropdown-item-5 .dropdown-text { color: #2e7d32; }

/* 禁用状态 */
.el-dropdown-menu__item.is-disabled .dropdown-text {
  color: #c0c4cc !important;
}

/* 备注信息 */
.remarks-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remarks-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.remarks-content {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #409EFF;
}

.remarks-content p {
  margin: 0;
  color: #303133;
  line-height: 1.6;
  font-size: 14px;
}
</style>
