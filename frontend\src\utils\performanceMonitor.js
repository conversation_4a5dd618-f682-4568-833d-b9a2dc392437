/**
 * 前端性能监控工具
 * 监控页面加载、API请求、图表渲染等性能指标
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  /**
   * 开始性能测量
   */
  startMeasure(name) {
    if (!this.isEnabled) return

    const startTime = performance.now()
    this.metrics.set(name, {
      startTime,
      endTime: null,
      duration: null,
      type: 'measure'
    })

    // 使用Performance API标记
    if (performance.mark) {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * 结束性能测量
   */
  endMeasure(name) {
    if (!this.isEnabled) return

    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance measure "${name}" not found`)
      return
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    // 使用Performance API标记
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
    }

    // 输出性能信息
    this.logPerformance(name, duration)

    return duration
  }

  /**
   * 测量函数执行时间
   */
  async measureFunction(name, fn) {
    if (!this.isEnabled) {
      return await fn()
    }

    this.startMeasure(name)
    try {
      const result = await fn()
      return result
    } finally {
      this.endMeasure(name)
    }
  }

  /**
   * 测量API请求性能
   */
  async measureApiRequest(name, requestFn) {
    if (!this.isEnabled) {
      return await requestFn()
    }

    const startTime = performance.now()
    
    try {
      const result = await requestFn()
      const duration = performance.now() - startTime
      
      this.logApiPerformance(name, duration, 'success')
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      this.logApiPerformance(name, duration, 'error', error)
      throw error
    }
  }

  /**
   * 监控图表渲染性能
   */
  measureChartRender(chartName, renderFn) {
    if (!this.isEnabled) {
      return renderFn()
    }

    return this.measureFunction(`chart-render-${chartName}`, renderFn)
  }

  /**
   * 监控页面加载性能
   */
  measurePageLoad() {
    if (!this.isEnabled) return

    // 等待页面完全加载
    if (document.readyState === 'complete') {
      this.collectPageMetrics()
    } else {
      window.addEventListener('load', () => {
        this.collectPageMetrics()
      })
    }
  }

  /**
   * 收集页面性能指标
   */
  collectPageMetrics() {
    if (!performance.timing) return

    const timing = performance.timing
    const metrics = {
      // DNS查询时间
      dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
      // TCP连接时间
      tcpConnect: timing.connectEnd - timing.connectStart,
      // 请求响应时间
      request: timing.responseEnd - timing.requestStart,
      // DOM解析时间
      domParse: timing.domContentLoadedEventEnd - timing.domLoading,
      // 页面完全加载时间
      pageLoad: timing.loadEventEnd - timing.navigationStart
    }

    console.group('📊 页面性能指标')
    Object.entries(metrics).forEach(([key, value]) => {
      console.log(`${key}: ${value}ms`)
    })
    console.groupEnd()

    // 检查性能问题
    this.checkPerformanceIssues(metrics)
  }

  /**
   * 检查性能问题
   */
  checkPerformanceIssues(metrics) {
    const issues = []

    if (metrics.dnsLookup > 100) {
      issues.push('DNS查询时间过长')
    }
    if (metrics.tcpConnect > 100) {
      issues.push('TCP连接时间过长')
    }
    if (metrics.request > 1000) {
      issues.push('请求响应时间过长')
    }
    if (metrics.domParse > 500) {
      issues.push('DOM解析时间过长')
    }
    if (metrics.pageLoad > 3000) {
      issues.push('页面加载时间过长')
    }

    if (issues.length > 0) {
      console.warn('⚠️ 发现性能问题:', issues)
    }
  }

  /**
   * 监控内存使用
   */
  measureMemoryUsage() {
    if (!this.isEnabled || !performance.memory) return

    const memory = performance.memory
    const metrics = {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
    }

    console.log(`💾 内存使用: ${metrics.used}MB / ${metrics.total}MB (限制: ${metrics.limit}MB)`)

    // 检查内存泄漏
    if (metrics.used / metrics.limit > 0.8) {
      console.warn('⚠️ 内存使用率过高，可能存在内存泄漏')
    }

    return metrics
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if (!this.isEnabled || !window.PerformanceObserver) return

    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) {
            console.warn(`⏱️ 长任务检测: ${entry.name} 耗时 ${entry.duration.toFixed(2)}ms`)
          }
        })
      })

      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    } catch (error) {
      console.warn('长任务监控不支持:', error)
    }
  }

  /**
   * 输出性能日志
   */
  logPerformance(name, duration) {
    const color = duration > 1000 ? 'color: red' : duration > 500 ? 'color: orange' : 'color: green'
    console.log(`%c⏱️ ${name}: ${duration.toFixed(2)}ms`, color)
  }

  /**
   * 输出API性能日志
   */
  logApiPerformance(name, duration, status, error = null) {
    const statusColor = status === 'success' ? 'color: green' : 'color: red'
    const durationColor = duration > 2000 ? 'color: red' : duration > 1000 ? 'color: orange' : 'color: blue'
    
    console.log(`%c🌐 API ${name} [${status}]: %c${duration.toFixed(2)}ms`, statusColor, durationColor)
    
    if (error) {
      console.error('API错误详情:', error)
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const report = {
      measures: Array.from(this.metrics.entries()).map(([name, metric]) => ({
        name,
        duration: metric.duration,
        type: metric.type
      })),
      memory: this.measureMemoryUsage(),
      timestamp: new Date().toISOString()
    }

    return report
  }

  /**
   * 清除性能数据
   */
  clear() {
    this.metrics.clear()
    
    // 清除Performance API数据
    if (performance.clearMarks) {
      performance.clearMarks()
    }
    if (performance.clearMeasures) {
      performance.clearMeasures()
    }
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.clear()
    
    // 断开所有观察器
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers = []
  }

  /**
   * 启用/禁用监控
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()

// 自动开始页面性能监控
performanceMonitor.measurePageLoad()
performanceMonitor.observeLongTasks()

export default PerformanceMonitor
