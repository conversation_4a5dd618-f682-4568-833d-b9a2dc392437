package com.haitao.backend.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建任务请求DTO
 */
@Data
public class CreateTaskRequest {

    /** 项目名称 */
    private String projectName;

    /** 订单编号（可选，留空自动生成） */
    private String orderNumber;

    /** 项目总价 */
    private BigDecimal totalPrice;

    /** 抽成比例 */
    private BigDecimal commissionRate;

    /** 派单员抽成金额 */
    private BigDecimal dispatcherFee;

    /** 分配成员ID */
    private Long assignedUserId;

    /** 接单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;

    /** 客户要求完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;

    /** 备注信息 */
    private String remarks;

    /** 任务状态（创建时忽略，由后端设置默认值0） */
    @JsonIgnore
    private Integer status;
}
