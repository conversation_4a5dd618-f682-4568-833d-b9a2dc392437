package com.haitao.backend.config;

import com.haitao.backend.interceptor.JwtInterceptor;
import com.haitao.backend.interceptor.LoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private JwtInterceptor jwtInterceptor;
    
    @Autowired
    private LoggingInterceptor loggingInterceptor;
    


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加日志拦截器（最高优先级）
        registry.addInterceptor(loggingInterceptor)
                .addPathPatterns("/**")
                .order(1);

        // 添加JWT认证拦截器
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/auth/login",
                    "/health",
                    "/error",
                    "/favicon.ico"
                )
                .order(2);
    }
}
