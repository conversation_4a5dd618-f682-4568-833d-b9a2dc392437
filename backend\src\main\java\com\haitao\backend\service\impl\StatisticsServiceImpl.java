package com.haitao.backend.service.impl;

import com.haitao.backend.dto.*;
import com.haitao.backend.entity.User;
import com.haitao.backend.exception.BusinessException;
import com.haitao.backend.mapper.OrderTaskMapper;
import com.haitao.backend.mapper.UserMapper;
import com.haitao.backend.service.StatisticsService;
import com.haitao.backend.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计服务实现类
 */
@Service
public class StatisticsServiceImpl implements StatisticsService {
    
    @Autowired
    private OrderTaskMapper orderTaskMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserService userService;
    
    @Override
    public IncomeStatisticsResponse getIncomeStatistics(StatisticsQueryRequest query, Long operatorId) {
        // 权限检查
        validatePermission(operatorId, query.getUserId());
        
        // 参数验证和默认值设置
        validateAndSetDefaults(query);
        
        IncomeStatisticsResponse response = new IncomeStatisticsResponse();
        
        // 获取总体统计数据
        IncomeStatisticsResponse.OverallStatistics overall = orderTaskMapper.selectOverallStatistics(query);
        response.setOverall(overall);
        
        // 获取趋势数据
        List<IncomeStatisticsResponse.TrendData> trendData = orderTaskMapper.selectTrendData(query);
        response.setTrendData(trendData);
        
        // 获取用户排行数据
        List<IncomeStatisticsResponse.UserRankingData> userRanking = orderTaskMapper.selectUserRankingData(query);
        // 设置排名
        for (int i = 0; i < userRanking.size(); i++) {
            userRanking.get(i).setRanking(i + 1);
        }
        response.setUserRanking(userRanking);
        
        // 构建图表数据
        IncomeStatisticsResponse.ChartData chartData = buildChartData(trendData);
        response.setChartData(chartData);
        
        return response;
    }
    
    @Override
    public UserIncomeDetailResponse getUserIncomeDetail(Long userId, StatisticsQueryRequest query, Long operatorId) {
        // 权限检查
        validatePermission(operatorId, userId);
        
        // 参数验证
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        validateAndSetDefaults(query);
        
        UserIncomeDetailResponse response = new UserIncomeDetailResponse();
        
        // 设置用户基本信息
        UserIncomeDetailResponse.UserInfo userInfo = new UserIncomeDetailResponse.UserInfo();
        userInfo.setUserId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setRoleName(user.getRole() == 0 ? "管理员" : "普通成员");
        userInfo.setPhone(user.getPhone());
        userInfo.setEmail(user.getEmail());
        response.setUserInfo(userInfo);
        
        // 获取收入汇总数据
        UserIncomeDetailResponse.IncomeSummary incomeSummary = orderTaskMapper.selectUserIncomeSummary(userId, query);
        response.setIncomeSummary(incomeSummary);
        
        // 获取任务详情列表
        List<UserIncomeDetailResponse.TaskIncomeDetail> taskDetails = orderTaskMapper.selectUserTaskIncomeDetails(userId, query);
        response.setTaskDetails(taskDetails);
        
        return response;
    }
    
    @Override
    public IncomeStatisticsResponse.ChartData getIncomeChartData(StatisticsQueryRequest query, Long operatorId) {
        // 权限检查
        validatePermission(operatorId, query.getUserId());
        
        validateAndSetDefaults(query);
        
        // 获取趋势数据
        List<IncomeStatisticsResponse.TrendData> trendData = orderTaskMapper.selectTrendData(query);
        
        return buildChartData(trendData);
    }
    
    @Override
    public IncomeStatisticsResponse getUserRanking(StatisticsQueryRequest query, Long operatorId) {
        // 权限检查
        validatePermission(operatorId, null);
        
        validateAndSetDefaults(query);
        
        // 设置默认排序和限制
        if (!StringUtils.hasText(query.getSortBy())) {
            query.setSortBy("totalAmount");
        }
        if (query.getLimit() == null) {
            query.setLimit(10);
        }
        
        IncomeStatisticsResponse response = new IncomeStatisticsResponse();
        
        List<IncomeStatisticsResponse.UserRankingData> userRanking = orderTaskMapper.selectUserRankingData(query);
        // 设置排名
        for (int i = 0; i < userRanking.size(); i++) {
            userRanking.get(i).setRanking(i + 1);
        }
        response.setUserRanking(userRanking);
        
        return response;
    }
    
    @Override
    public IncomeStatisticsResponse getDispatcherRanking(StatisticsQueryRequest query, Long operatorId) {
        // 权限检查 - 只有管理员可以查看派单员排行
        if (!userService.isAdmin(operatorId)) {
            throw new BusinessException("只有管理员可以查看派单员排行");
        }
        
        validateAndSetDefaults(query);
        
        if (query.getLimit() == null) {
            query.setLimit(10);
        }
        
        List<Map<String, Object>> dispatcherData = orderTaskMapper.selectDispatcherFeeRanking(query);
        
        IncomeStatisticsResponse response = new IncomeStatisticsResponse();
        
        // 转换为用户排行数据格式（复用结构）
        List<IncomeStatisticsResponse.UserRankingData> userRanking = new ArrayList<>();
        for (int i = 0; i < dispatcherData.size(); i++) {
            Map<String, Object> data = dispatcherData.get(i);
            IncomeStatisticsResponse.UserRankingData ranking = new IncomeStatisticsResponse.UserRankingData();
            ranking.setUserId(((Number) data.get("userId")).longValue());
            ranking.setRealName((String) data.get("realName"));
            ranking.setUsername((String) data.get("username"));
            ranking.setNetIncome((BigDecimal) data.get("totalDispatcherFee")); // 使用netIncome字段存储派单员抽成
            ranking.setTaskCount(((Number) data.get("taskCount")).longValue());
            ranking.setRanking(i + 1);
            userRanking.add(ranking);
        }
        
        response.setUserRanking(userRanking);
        return response;
    }
    
    /**
     * 权限验证
     */
    private void validatePermission(Long operatorId, Long targetUserId) {
        if (operatorId == null) {
            throw new BusinessException("操作者ID不能为空");
        }
        
        // 如果查询特定用户的数据，需要验证权限
        if (targetUserId != null) {
            // 管理员可以查看所有用户数据，普通用户只能查看自己的数据
            if (!userService.isAdmin(operatorId) && !operatorId.equals(targetUserId)) {
                throw new BusinessException("您只能查看自己的统计数据");
            }
        } else {
            // 查看全局统计数据需要管理员权限
            if (!userService.isAdmin(operatorId)) {
                throw new BusinessException("只有管理员可以查看全局统计数据");
            }
        }
    }
    
    /**
     * 参数验证和默认值设置
     */
    private void validateAndSetDefaults(StatisticsQueryRequest query) {
        if (query == null) {
            throw new BusinessException("查询参数不能为空");
        }
        
        // 设置默认时间维度
        if (!StringUtils.hasText(query.getTimeDimension())) {
            query.setTimeDimension("month");
        }
        
        // 设置默认时间范围（最近3个月）
        if (query.getStartTime() == null || query.getEndTime() == null) {
            LocalDateTime now = LocalDateTime.now();
            query.setEndTime(now);
            query.setStartTime(now.minus(3, ChronoUnit.MONTHS));
        }
        
        // 验证时间范围
        if (query.getStartTime().isAfter(query.getEndTime())) {
            throw new BusinessException("开始时间不能晚于结束时间");
        }
        
        // 设置默认统计类型
        if (!StringUtils.hasText(query.getStatisticsType())) {
            query.setStatisticsType("income");
        }
    }
    
    /**
     * 构建图表数据
     */
    private IncomeStatisticsResponse.ChartData buildChartData(List<IncomeStatisticsResponse.TrendData> trendData) {
        IncomeStatisticsResponse.ChartData chartData = new IncomeStatisticsResponse.ChartData();
        
        List<String> xAxisLabels = new ArrayList<>();
        List<BigDecimal> totalAmountSeries = new ArrayList<>();
        List<BigDecimal> netIncomeSeries = new ArrayList<>();
        List<BigDecimal> dispatcherFeeSeries = new ArrayList<>();
        List<Long> taskCountSeries = new ArrayList<>();
        
        for (IncomeStatisticsResponse.TrendData data : trendData) {
            xAxisLabels.add(data.getTimeLabel());
            totalAmountSeries.add(data.getTotalAmount());
            netIncomeSeries.add(data.getNetIncome());
            dispatcherFeeSeries.add(data.getDispatcherFee());
            taskCountSeries.add(data.getTaskCount());
        }
        
        chartData.setXAxisLabels(xAxisLabels);
        chartData.setTotalAmountSeries(totalAmountSeries);
        chartData.setNetIncomeSeries(netIncomeSeries);
        chartData.setDispatcherFeeSeries(dispatcherFeeSeries);
        chartData.setTaskCountSeries(taskCountSeries);
        
        return chartData;
    }
}
