package com.haitao.backend.enums;

/**
 * 用户角色枚举
 */
public enum UserRole {
    
    ADMIN(0, "管理员"),
    MEMBER(1, "普通成员");
    
    private final Integer code;
    private final String name;
    
    UserRole(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public static UserRole fromCode(Integer code) {
        for (UserRole role : values()) {
            if (role.code.equals(code)) {
                return role;
            }
        }
        return null;
    }
}
