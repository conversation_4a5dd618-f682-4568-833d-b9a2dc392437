package com.haitao.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.common.ErrorMessages;
import com.haitao.backend.entity.OrderTask;
import com.haitao.backend.entity.User;
import com.haitao.backend.dto.*;
import com.haitao.backend.exception.BusinessException;
import com.haitao.backend.mapper.OrderTaskMapper;
import com.haitao.backend.mapper.UserMapper;
import com.haitao.backend.service.OrderTaskService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单任务服务实现类
 */
@Service
public class OrderTaskServiceImpl implements OrderTaskService {
    
    @Autowired
    private OrderTaskMapper orderTaskMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    @Transactional
    public OrderTask createTask(CreateTaskRequest request, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 验证请求参数
        validateCreateTaskRequest(request);

        // 验证被分配用户是否存在
        if (request.getAssignedUserId() != null) {
            User assignedUser = userMapper.selectById(request.getAssignedUserId());
            if (assignedUser == null) {
                throw new BusinessException("指定的分配用户不存在");
            }
        }
        
        // 创建任务对象
        OrderTask task = new OrderTask();
        BeanUtils.copyProperties(request, task);

        // 处理订单编号
        String orderNumber = request.getOrderNumber();
        if (!StringUtils.hasText(orderNumber)) {
            // 如果没有提供订单编号，则自动生成
            orderNumber = orderTaskMapper.generateOrderNumber();
            if (!StringUtils.hasText(orderNumber)) {
                orderNumber = generateDefaultOrderNumber();
            }
        }
        task.setOrderNumber(orderNumber);

        // 计算财务数据（基于前端传入的数据）
        calculateTaskFinance(task);

        // 设置创建人
        task.setCreatedBy(operatorId);

        // 设置默认状态
        task.setStatus(0); // 待处理

        // 设置创建时间
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        
        // 保存任务
        orderTaskMapper.insert(task);
        
        return task;
    }
    
    @Override
    @Transactional
    public OrderTask updateTask(Long taskId, UpdateTaskRequest request, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 检查任务是否存在
        OrderTask existingTask = orderTaskMapper.selectById(taskId);
        if (existingTask == null) {
            throw new BusinessException("任务不存在");
        }

        // 验证请求参数
        validateUpdateTaskRequest(request);

        // 验证被分配用户是否存在
        if (request.getAssignedUserId() != null) {
            User assignedUser = userMapper.selectById(request.getAssignedUserId());
            if (assignedUser == null) {
                throw new BusinessException("指定的分配用户不存在");
            }
        }
        
        // 更新任务信息
        BeanUtils.copyProperties(request, existingTask);

        // 重新计算财务数据（基于前端传入的数据）
        calculateTaskFinance(existingTask);

        existingTask.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        orderTaskMapper.updateById(existingTask);
        
        return existingTask;
    }
    
    @Override
    @Transactional
    public void deleteTask(Long taskId, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }
        
        // 检查任务是否存在
        OrderTask task = orderTaskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        
        // 逻辑删除
        orderTaskMapper.deleteById(taskId);
    }

    @Override
    @Transactional
    public void batchDeleteTasks(List<Long> taskIds, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        if (taskIds == null || taskIds.isEmpty()) {
            throw new BusinessException("任务ID列表不能为空");
        }

        // 检查所有任务是否存在
        List<OrderTask> tasks = orderTaskMapper.selectList(
            new LambdaQueryWrapper<OrderTask>().in(OrderTask::getId, taskIds)
        );
        if (tasks.size() != taskIds.size()) {
            throw new BusinessException("部分任务不存在");
        }

        // 批量逻辑删除
        orderTaskMapper.delete(
            new LambdaQueryWrapper<OrderTask>().in(OrderTask::getId, taskIds)
        );
    }

    @Override
    public IPage<TaskListResponse> getTaskList(TaskQueryRequest query) {
        Page<TaskListResponse> page = new Page<>(query.getPage(), query.getSize());
        return orderTaskMapper.selectTaskListWithUserInfo(page, query);
    }
    
    @Override
    public TaskListResponse getTaskDetail(Long taskId) {
        TaskListResponse task = orderTaskMapper.selectTaskDetailById(taskId);
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        return task;
    }
    
    @Override
    @Transactional
    public void updateTaskStatus(Long taskId, Integer status, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }
        
        // 检查任务是否存在
        OrderTask task = orderTaskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        
        // 验证状态值
        if (status < 0 || status > 5) {
            throw new BusinessException("无效的任务状态");
        }
        
        // 更新状态
        task.setStatus(status);
        task.setUpdateTime(LocalDateTime.now());
        orderTaskMapper.updateById(task);
    }
    
    @Override
    @Transactional
    public void assignTask(Long taskId, Long assignedUserId, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }
        
        // 检查任务是否存在
        OrderTask task = orderTaskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        
        // 验证被分配用户是否存在
        if (assignedUserId != null) {
            User assignedUser = userMapper.selectById(assignedUserId);
            if (assignedUser == null) {
                throw new BusinessException("指定的分配用户不存在");
            }
        }
        
        // 分配任务
        task.setAssignedUserId(assignedUserId);
        task.setUpdateTime(LocalDateTime.now());
        orderTaskMapper.updateById(task);
    }
    
    /**
     * 检查用户是否为管理员
     */
    private boolean isAdmin(Long userId) {
        User user = userMapper.selectById(userId);
        return user != null && user.getRole() == 0;
    }
    
    /**
     * 生成默认订单编号
     */
    private String generateDefaultOrderNumber() {
        return "ORD" + System.currentTimeMillis();
    }

    /**
     * 计算任务财务数据
     */
    private void calculateTaskFinance(OrderTask task) {
        if (task.getTotalPrice() != null && task.getCommissionRate() != null) {
            // 计算抽成金额
            BigDecimal commissionAmount = task.getTotalPrice().multiply(task.getCommissionRate());
            task.setCommissionAmount(commissionAmount);

            // 计算实得收入
            BigDecimal netIncome = task.getTotalPrice().subtract(commissionAmount);
            task.setNetIncome(netIncome);

            // 派单员抽成由前端传入，不在这里计算
        }
    }

    /**
     * 验证创建任务请求参数
     */
    private void validateCreateTaskRequest(CreateTaskRequest request) {
        // 验证项目名称
        if (!StringUtils.hasText(request.getProjectName())) {
            throw new BusinessException("项目名称不能为空");
        }
        if (request.getProjectName().length() > 100) {
            throw new BusinessException("项目名称长度不能超过100个字符");
        }

        // 验证订单编号（如果提供）
        if (StringUtils.hasText(request.getOrderNumber()) && request.getOrderNumber().length() > 100) {
            throw new BusinessException("订单编号长度不能超过100个字符");
        }

        // 验证项目总价
        if (request.getTotalPrice() == null) {
            throw new BusinessException("项目总价不能为空");
        }
        if (request.getTotalPrice().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            throw new BusinessException("项目总价必须大于0");
        }

        // 验证抽成比例
        if (request.getCommissionRate() == null) {
            throw new BusinessException("抽成比例不能为空");
        }
        if (request.getCommissionRate().compareTo(BigDecimal.ZERO) < 0 ||
            request.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
            throw new BusinessException("抽成比例必须在0-1之间");
        }

        // 验证派单员抽成金额
        if (request.getDispatcherFee() != null &&
            request.getDispatcherFee().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("派单员抽成金额不能小于0");
        }

        // 验证接单时间
        if (request.getOrderTime() == null) {
            throw new BusinessException("接单时间不能为空");
        }

        // 验证截止时间（如果提供）
        if (request.getDeadline() != null) {
            if (request.getDeadline().isBefore(request.getOrderTime()) ||
                request.getDeadline().isEqual(request.getOrderTime())) {
                throw new BusinessException("截止时间必须晚于接单时间");
            }
        }

        // 验证备注信息长度
        if (request.getRemarks() != null && request.getRemarks().length() > 500) {
            throw new BusinessException("备注信息长度不能超过500个字符");
        }
    }

    /**
     * 验证更新任务请求参数
     */
    private void validateUpdateTaskRequest(UpdateTaskRequest request) {
        // 验证项目名称
        if (!StringUtils.hasText(request.getProjectName())) {
            throw new BusinessException("项目名称不能为空");
        }
        if (request.getProjectName().length() > 100) {
            throw new BusinessException("项目名称长度不能超过100个字符");
        }

        // 验证订单编号
        if (!StringUtils.hasText(request.getOrderNumber())) {
            throw new BusinessException("订单编号不能为空");
        }
        if (request.getOrderNumber().length() > 100) {
            throw new BusinessException("订单编号长度不能超过100个字符");
        }

        // 验证项目总价
        if (request.getTotalPrice() == null) {
            throw new BusinessException("项目总价不能为空");
        }
        if (request.getTotalPrice().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            throw new BusinessException("项目总价必须大于0");
        }

        // 验证抽成比例
        if (request.getCommissionRate() == null) {
            throw new BusinessException("抽成比例不能为空");
        }
        if (request.getCommissionRate().compareTo(BigDecimal.ZERO) < 0 ||
            request.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
            throw new BusinessException("抽成比例必须在0-1之间");
        }

        // 验证派单员抽成金额
        if (request.getDispatcherFee() != null &&
            request.getDispatcherFee().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("派单员抽成金额不能小于0");
        }

        // 验证接单时间
        if (request.getOrderTime() == null) {
            throw new BusinessException("接单时间不能为空");
        }

        // 验证截止时间（如果提供）
        if (request.getDeadline() != null) {
            if (request.getDeadline().isBefore(request.getOrderTime()) ||
                request.getDeadline().isEqual(request.getOrderTime())) {
                throw new BusinessException("截止时间必须晚于接单时间");
            }
        }

        // 验证任务状态
        if (request.getStatus() != null && (request.getStatus() < 0 || request.getStatus() > 5)) {
            throw new BusinessException("任务状态值无效");
        }

        // 验证备注信息长度
        if (request.getRemarks() != null && request.getRemarks().length() > 500) {
            throw new BusinessException("备注信息长度不能超过500个字符");
        }
    }
}
