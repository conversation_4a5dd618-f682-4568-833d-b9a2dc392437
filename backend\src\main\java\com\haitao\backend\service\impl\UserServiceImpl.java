package com.haitao.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haitao.backend.common.ErrorMessages;
import com.haitao.backend.dto.*;
import com.haitao.backend.entity.User;
import com.haitao.backend.exception.BusinessException;
import com.haitao.backend.mapper.UserMapper;
import com.haitao.backend.service.UserService;
import com.haitao.backend.util.JwtUtil;
import com.haitao.backend.util.MD5Util;
import com.haitao.backend.util.QueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;

    // 默认密码
    private static final String DEFAULT_PASSWORD = "123456";
    
    @Override
    public LoginResponse login(LoginRequest request) {
        // 先查询用户（不限制状态）
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getUsername, request.getUsername());
        User user = userMapper.selectOne(queryWrapper);

        // 用户不存在
        if (user == null) {
            throw new BusinessException(ErrorMessages.LOGIN_FAILED);
        }

        // 验证密码
        String encryptedPassword = MD5Util.encrypt(request.getPassword());
        if (!encryptedPassword.equals(user.getPassword())) {
            throw new BusinessException(ErrorMessages.LOGIN_FAILED);
        }

        // 检查账号状态
        if (user.getStatus() == 0) {
            throw new BusinessException(ErrorMessages.ACCOUNT_DISABLED);
        }
        
        // 生成token
        String token = jwtUtil.generateToken(user.getId(), user.getUsername(), 
                request.getRememberMe() != null && request.getRememberMe());
        
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setRealName(user.getRealName());
        response.setRole(user.getRole());
        response.setExpiresIn(request.getRememberMe() != null && request.getRememberMe() ? 
                7 * 24 * 60 * 60L : 24 * 60 * 60L);
        
        return response;
    }
    
    @Override
    public User createUser(CreateUserRequest request, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 参数验证
        if (!StringUtils.hasText(request.getUsername())) {
            throw new BusinessException(ErrorMessages.USERNAME_REQUIRED);
        }
        if (!request.getUsername().matches("^[a-zA-Z0-9_]{3,20}$")) {
            throw new BusinessException(ErrorMessages.USERNAME_INVALID);
        }
        if (!StringUtils.hasText(request.getRealName())) {
            throw new BusinessException(ErrorMessages.REAL_NAME_REQUIRED);
        }
        if (request.getRole() == null) {
            throw new BusinessException(ErrorMessages.ROLE_REQUIRED);
        }
        if (request.getRole() != 0 && request.getRole() != 1) {
            throw new BusinessException(ErrorMessages.ROLE_INVALID);
        }

        // 检查用户名是否已存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getUsername, request.getUsername());
        if (userMapper.selectOne(queryWrapper) != null) {
            throw new BusinessException(ErrorMessages.USERNAME_EXISTS);
        }

        User user = new User();
        user.setUsername(request.getUsername());
        user.setRealName(request.getRealName());
        user.setRole(request.getRole());
        user.setPhone(request.getPhone());
        user.setEmail(request.getEmail());
        user.setStatus(1); // 默认启用

        // 设置密码
        String password = StringUtils.hasText(request.getInitialPassword()) ?
                         request.getInitialPassword() : DEFAULT_PASSWORD;
        user.setPassword(MD5Util.encrypt(password));

        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        userMapper.insert(user);
        return user;
    }
    
    @Override
    public User updateUser(Long userId, UpdateUserRequest request, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ErrorMessages.USER_NOT_FOUND);
        }

        // 不能修改自己的角色
        if (userId.equals(operatorId) && request.getRole() != null) {
            throw new BusinessException(ErrorMessages.CANNOT_MODIFY_SELF_ROLE);
        }

        // 更新用户信息（用户名不可修改）
        if (StringUtils.hasText(request.getRealName())) {
            user.setRealName(request.getRealName());
        }
        if (request.getRole() != null) {
            user.setRole(request.getRole());
        }
        if (StringUtils.hasText(request.getPhone())) {
            user.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getEmail())) {
            user.setEmail(request.getEmail());
        }
        if (request.getStatus() != null) {
            // 不能停用自己
            if (userId.equals(operatorId) && request.getStatus() == 0) {
                throw new BusinessException(ErrorMessages.CANNOT_DISABLE_SELF);
            }
            user.setStatus(request.getStatus());
        }

        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
        return user;
    }

    @Override
    public void deleteUser(Long userId, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 不能删除自己
        if (userId.equals(operatorId)) {
            throw new BusinessException(ErrorMessages.CANNOT_DELETE_SELF);
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ErrorMessages.USER_NOT_FOUND);
        }

        userMapper.deleteById(userId);
    }

    @Override
    public void batchDeleteUsers(List<Long> userIds, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        if (userIds == null || userIds.isEmpty()) {
            throw new BusinessException("用户ID列表不能为空");
        }

        // 检查是否包含操作者自己
        if (userIds.contains(operatorId)) {
            throw new BusinessException(ErrorMessages.CANNOT_DELETE_SELF);
        }

        // 检查所有用户是否存在
        List<User> users = userMapper.selectList(
            new LambdaQueryWrapper<User>().in(User::getId, userIds)
        );
        if (users.size() != userIds.size()) {
            throw new BusinessException("部分用户不存在");
        }

        // 批量删除
        userMapper.delete(
            new LambdaQueryWrapper<User>().in(User::getId, userIds)
        );
    }

    @Override
    public void resetPassword(Long userId, ResetPasswordRequest request, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 参数验证
        if (!StringUtils.hasText(request.getNewPassword())) {
            throw new BusinessException(ErrorMessages.PASSWORD_REQUIRED);
        }
        if (request.getNewPassword().length() < 6) {
            throw new BusinessException(ErrorMessages.PASSWORD_TOO_SHORT);
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ErrorMessages.USER_NOT_FOUND);
        }

        // 检查目标用户是否为管理员，管理员密码不能被重置
        if (user.getRole() == 0) {
            throw new BusinessException("不能重置管理员密码");
        }

        user.setPassword(MD5Util.encrypt(request.getNewPassword()));
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    public void toggleUserStatus(Long userId, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 不能停用自己
        if (userId.equals(operatorId)) {
            throw new BusinessException(ErrorMessages.CANNOT_DISABLE_SELF);
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ErrorMessages.USER_NOT_FOUND);
        }

        user.setStatus(user.getStatus() == 1 ? 0 : 1);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    public PageResponse<UserListResponse> getUserList(PageRequest request, Long operatorId) {
        // 检查操作者是否为管理员
        if (!isAdmin(operatorId)) {
            throw new BusinessException(ErrorMessages.ADMIN_REQUIRED);
        }

        // 使用QueryUtil构建查询条件
        LambdaQueryWrapper<User> queryWrapper = QueryUtil.<User>builder()
                .eq(request.getRole() != null, User::getRole, request.getRole())
                .eq(request.getStatus() != null, User::getStatus, request.getStatus())
                .and(StringUtils.hasText(request.getKeyword()), wrapper -> {
                    wrapper.like(User::getUsername, request.getKeyword())
                           .or()
                           .like(User::getRealName, request.getKeyword());
                })
                .orderByDesc(User::getCreateTime)
                .build();

        // 分页查询
        Page<User> page = new Page<>(request.getPage(), request.getSize());
        IPage<User> userPage = userMapper.selectPage(page, queryWrapper);

        // 转换为响应DTO
        List<UserListResponse> responseList = userPage.getRecords().stream()
                .map(this::convertToUserListResponse)
                .collect(Collectors.toList());

        return new PageResponse<>(responseList, userPage.getTotal(),
                                request.getPage(), request.getSize());
    }

    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public boolean isAdmin(Long userId) {
        User user = userMapper.selectById(userId);
        return user != null && user.getRole() == 0;
    }

    /**
     * 转换为用户列表响应DTO
     */
    private UserListResponse convertToUserListResponse(User user) {
        UserListResponse response = new UserListResponse();
        BeanUtils.copyProperties(user, response);

        // 设置角色名称
        response.setRoleName(user.getRole() == 0 ? "管理员" : "普通成员");

        // 设置状态名称
        response.setStatusName(user.getStatus() == 1 ? "正常" : "停用");

        return response;
    }
}
